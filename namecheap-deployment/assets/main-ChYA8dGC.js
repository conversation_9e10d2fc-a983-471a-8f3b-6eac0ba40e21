const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/HomePage-DnA1_CHD.js","assets/react-core-B9nwsbCA.js","assets/router-BDggJ1ol.js","assets/VideoGrid-BixslH7t.js","assets/ui-components-DIfZPJu4.js","assets/utils-CThq6s06.js","assets/state-DYB6TP8I.js","assets/supabase-C-51sAsE.js","assets/icons-BWE0bDFO.js","assets/useVideos-BvCn9-Hj.js","assets/VideoPage-DqkAF9sY.js","assets/NativeVideoPlayer-C7XQiAop.js","assets/CategoryPage-BwGONFou.js","assets/AllVideosPage-Zolo6wwJ.js","assets/UploadPage-Bt8MLRuf.js","assets/ManageVideosPage-D0SeC-xi.js","assets/SearchPage-CzYoNyun.js","assets/FavoritesPage-C7zymwKt.js","assets/TestVideoPage-9Xn_kgid.js","assets/TestVideoIndexPage-CVOUSkL9.js","assets/AdminPage-DB-wGY7S.js"])))=>i.map(i=>d[i]);
import{r as e,a as t,j as r,c as s}from"./react-core-B9nwsbCA.js";import{i as a,s as i,j as o,k as n,u as l,l as c,m as d,n as u,o as m,p as h,q as g,r as x,S as p}from"./utils-CThq6s06.js";import{_ as f}from"./supabase-C-51sAsE.js";import{u as w,B as v,R as b,a as y}from"./router-BDggJ1ol.js";import{B as j,I as N}from"./ui-components-DIfZPJu4.js";import{c as _,p as C}from"./state-DYB6TP8I.js";import{S as k,X as L,T as E,e as S,B as A,U as V,f as z,g as U,M as I,H as P,V as D,h as T,i as M,F as O,j as R,k as F,l as q,m as H}from"./icons-BWE0bDFO.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const B=_(((e,t)=>({searchQuery:"",searchResults:[],recentSearches:JSON.parse(localStorage.getItem("recentSearches")||"[]"),isLoading:!1,error:null,setSearchQuery:t=>{e({searchQuery:t})},search:async r=>{const s=r||t().searchQuery;if(s.trim()){e({isLoading:!0,error:null});try{const{data:l,error:c}=await a(i.from("videos")).or(`title.ilike.%${s}%,description.ilike.%${s}%`).order("views",{ascending:!1}).limit(50);if(c)throw new Error(c.message);const d=l.map((e=>({id:e.id,title:e.title,description:e.description||"",thumbnailUrl:o(e.thumbnail_url||""),videoUrl:n(e.video_url||""),duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at||e.created_at,publishedAt:e.created_at,scheduledFor:void 0,status:"public",isHD:e.is_hd||!1,isPremium:!1,tags:Array.isArray(e.tags)?e.tags:[],category:e.category||"uncategorized",creator:{id:e.creator?.id||"",email:"",avatar:e.creator?.avatar_url||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}})));e({searchResults:d,isLoading:!1}),s.trim()&&r&&t().addRecentSearch(s)}catch(l){e({error:l instanceof Error?l.message:"An unknown error occurred",isLoading:!1})}}else e({searchResults:[],error:null})},clearSearch:()=>{e({searchQuery:"",searchResults:[]})},addRecentSearch:r=>{const s=r.trim();if(!s)return;const a=t().recentSearches.filter((e=>e.toLowerCase()!==s.toLowerCase())),i=[s,...a].slice(0,5);e({recentSearches:i}),localStorage.setItem("recentSearches",JSON.stringify(i))},clearRecentSearches:()=>{e({recentSearches:[]}),localStorage.removeItem("recentSearches")}}))),W=({className:s="",onClose:a})=>{const i=w(),{searchQuery:o,recentSearches:n,setSearchQuery:l,search:c,clearSearch:d,addRecentSearch:u,clearRecentSearches:m}=B(),[h,g]=e.useState(!1),x=e.useRef(null),p=e.useRef(null);t.useEffect((()=>{const e=e=>{p.current&&!p.current.contains(e.target)&&g(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[]);return r.jsx("div",{ref:p,className:`relative ${s}`,children:r.jsxs("form",{onSubmit:e=>{e.preventDefault(),o.trim()&&(u(o),g(!1),c(o),i(`/search?q=${encodeURIComponent(o)}`),a&&a())},className:"relative",children:[r.jsxs("div",{className:"relative flex items-center",children:[r.jsx("input",{ref:x,type:"text",placeholder:"Search videos",value:o,onChange:e=>{l(e.target.value),g(""===e.target.value)},onFocus:()=>{g(""===o)},className:"w-full bg-gray-800 text-white rounded-full pl-10 pr-10 py-2.5 focus:outline-none focus:ring-1 focus:ring-blue-700 border border-gray-700 hover:border-blue-700 transition-colors shadow-sm text-sm md:text-base mobile-search-input"}),r.jsx(k,{className:"absolute left-3 text-blue-500 search-icon",size:16}),o&&r.jsx("button",{type:"button",onClick:()=>{d(),x.current&&x.current.focus(),g(!0)},className:"absolute right-8 text-gray-400 hover:text-white p-1 search-button-mobile",children:r.jsx(L,{size:16})}),r.jsx("button",{type:"submit",className:"absolute right-2 text-blue-700 hover:text-blue-600 p-1 search-button-mobile",disabled:!o.trim(),children:r.jsx(k,{size:16})})]}),h&&n.length>0&&r.jsxs("div",{className:"absolute top-full left-0 right-0 mt-1 bg-gray-800 rounded-md shadow-lg z-50 overflow-hidden max-h-60 overflow-y-auto mobile-search-dropdown",children:[r.jsxs("div",{className:"flex items-center justify-between p-2 border-b border-gray-700",children:[r.jsx("h3",{className:"text-white font-medium text-sm",children:"Recent Searches"}),r.jsxs("button",{onClick:m,className:"text-gray-400 hover:text-white flex items-center text-xs",children:[r.jsx(E,{size:12,className:"mr-1"}),"Clear"]})]}),r.jsx("ul",{children:n.map(((e,t)=>r.jsx("li",{children:r.jsxs("button",{onClick:()=>(e=>{l(e),g(!1),c(e),i(`/search?q=${encodeURIComponent(e)}`),a&&a()})(e),className:"w-full text-left px-3 py-2 hover:bg-gray-700 flex items-center text-gray-300 hover:text-white text-sm truncate",children:[r.jsx(S,{size:14,className:"mr-2 text-gray-500 flex-shrink-0"}),r.jsx("span",{className:"truncate",children:e})]})},t)))})]})]})})},$=({onOpenSidebar:t,isAuthenticated:s=!1,onLoginClick:a,onSignUpClick:i})=>{const o=w(),{user:n,signOut:u}=l(),[m,h]=e.useState(!1),[g,x]=e.useState(!1),p=e.useRef(null),[f,v]=e.useState(!1),b=e.useRef(null),y=c(n?n.id:"guest");e.useEffect((()=>{const e=e=>{b.current&&!b.current.contains(e.target)&&v(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)}),[]);const N=async()=>{try{v(!1),await u(),o("/")}catch(e){v(!1),o("/")}};return e.useEffect((()=>{const e=()=>{h(window.scrollY>10)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)}),[]),e.useEffect((()=>{if(g&&p.current){const e=p.current.querySelector("input");e&&setTimeout((()=>{e.focus()}),100)}}),[g]),r.jsx("header",{className:`\n        fixed top-0 left-0 right-0 z-40 transition-all duration-300\n        ${m?"bg-gray-900/95 backdrop-blur-sm shadow-md":"bg-gradient-to-b from-gray-900 to-transparent"}\n      `,children:r.jsxs("div",{className:"container mx-auto px-4",children:[r.jsxs("div",{className:"hidden md:flex items-center justify-between h-16",children:[r.jsx("div",{className:"flex-shrink-0 flex items-center lg:ml-0",children:r.jsxs("button",{className:"font-bold text-xl text-white hover:opacity-80 transition-opacity",onClick:()=>o("/",{replace:!0}),children:[r.jsx("span",{className:"text-blue-500",children:"Blue"}),"Film"]})}),r.jsx("div",{className:"flex-1 max-w-xl mx-6",children:r.jsxs("div",{className:"relative search-bar-container",children:[r.jsx("div",{className:"absolute -top-6 left-4 text-blue-500 font-medium text-sm",children:"Search Videos"}),r.jsx(W,{className:"w-full"})]})}),r.jsx("div",{className:"flex items-center space-x-3",children:s?r.jsxs(r.Fragment,{children:[r.jsx("button",{className:"p-2 rounded-full hover:bg-gray-800 text-gray-300 hover:text-white",children:r.jsx(A,{size:22})}),r.jsx("button",{className:"p-2 rounded-full hover:bg-gray-800 text-gray-300 hover:text-white",onClick:()=>o("/upload"),title:"Upload Video",children:r.jsx(V,{size:22})}),r.jsxs("div",{className:"relative ml-2",ref:b,children:[r.jsx("button",{className:"h-9 w-9 rounded-full border-2 border-blue-700 overflow-hidden focus:outline-none focus:ring-2 focus:ring-blue-700 focus:ring-offset-1 focus:ring-offset-gray-900 bg-blue-100",onClick:()=>v(!f),children:r.jsx("img",{src:y,alt:"Profile",className:"h-full w-full object-contain",onError:e=>{e.currentTarget.src=d()}})}),f&&r.jsxs("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg py-1 z-60 border border-gray-700",children:[r.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors",onClick:()=>o("/manage"),children:"My Videos"}),r.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors",onClick:()=>o("/favorites"),children:"Favorites"}),r.jsx("div",{className:"border-t border-gray-700 my-1"}),r.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700 hover:text-red-300 transition-colors flex items-center space-x-2",onClick:N,children:[r.jsx(z,{size:16}),r.jsx("span",{children:"Log Out"})]})]})]})]}):r.jsxs(r.Fragment,{children:[r.jsx(j,{variant:"ghost",size:"sm",className:"hidden md:inline-flex",onClick:a,children:"Log in"}),r.jsx(j,{variant:"primary",size:"sm",leftIcon:r.jsx(U,{size:16}),onClick:i,children:"Sign Up"})]})})]}),r.jsx("div",{className:"md:hidden",children:r.jsxs("div",{className:"flex items-center justify-between h-16",children:[r.jsxs("div",{className:"flex items-center",children:[r.jsxs("button",{onClick:t,className:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-700","aria-expanded":"false",children:[r.jsx("span",{className:"sr-only",children:"Open main menu"}),r.jsx(I,{className:"block h-6 w-6","aria-hidden":"true"})]}),r.jsx("div",{className:"flex-shrink-0 flex items-center ml-2",children:r.jsxs("button",{className:"font-bold text-lg text-white hover:opacity-80 transition-opacity",onClick:()=>o("/",{replace:!0}),children:[r.jsx("span",{className:"text-blue-500",children:"Blue"}),"Film"]})})]}),r.jsx("div",{className:"flex items-center",children:s?r.jsxs(r.Fragment,{children:[r.jsx("button",{className:"p-1.5 rounded-full hover:bg-gray-800 text-gray-300 hover:text-white",onClick:()=>o("/upload"),title:"Upload Video",children:r.jsx(V,{size:18})}),r.jsxs("div",{className:"relative ml-1",ref:b,children:[r.jsx("button",{className:"h-7 w-7 rounded-full border-2 border-blue-700 overflow-hidden focus:outline-none focus:ring-2 focus:ring-blue-700 focus:ring-offset-1 focus:ring-offset-gray-900 bg-blue-100",onClick:()=>v(!f),children:r.jsx("img",{src:y,alt:"Profile",className:"h-full w-full object-contain",onError:e=>{e.currentTarget.src=d()}})}),f&&r.jsxs("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg py-1 z-60 border border-gray-700",children:[r.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors",onClick:()=>o("/manage"),children:"My Videos"}),r.jsx("button",{className:"w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white transition-colors",onClick:()=>o("/favorites"),children:"Favorites"}),r.jsx("div",{className:"border-t border-gray-700 my-1"}),r.jsxs("button",{className:"w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-gray-700 hover:text-red-300 transition-colors flex items-center space-x-2",onClick:N,children:[r.jsx(z,{size:16}),r.jsx("span",{children:"Log Out"})]})]})]})]}):r.jsx(j,{variant:"primary",size:"xs",leftIcon:r.jsx(U,{size:14}),onClick:i,children:"Sign Up"})})]})})]})})},Y=_(((e,t)=>({categories:[],featuredCategories:[],topCategories:[],isLoading:!1,error:null,fetchCategories:async()=>{e({isLoading:!0,error:null});try{const{data:t,error:r}=await i.from("categories").select("*").order("name");if(r)throw new Error(r.message);const s=t.map((e=>({id:e.id,name:e.name,slug:e.slug})));e({categories:s,isLoading:!1})}catch(t){e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},fetchFeaturedCategories:async()=>{e({isLoading:!0,error:null});try{const{data:t,error:r}=await i.from("videos").select("category").not("category","is",null);if(r)throw new Error(r.message);const s={};t.forEach((e=>{e.category&&(s[e.category]=(s[e.category]||0)+1)}));const a=Object.entries(s).sort((([,e],[,t])=>t-e)).slice(0,6).map((([e])=>e)),{data:o,error:n}=await i.from("categories").select("*").in("slug",a);if(n)throw new Error(n.message);const l=o.map((e=>({id:e.id,name:e.name,slug:e.slug})));e({featuredCategories:l,isLoading:!1})}catch(t){e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},fetchTopCategories:async(t=6)=>{e({isLoading:!0,error:null});try{const r=["hot","trending","new"],{data:s,error:a}=await i.from("videos").select("category").not("category","is",null);if(a)throw new Error(a.message);const o={};s.forEach((e=>{e.category&&(o[e.category]=(o[e.category]||0)+1)}));let n=Object.entries(o).map((([e,t])=>({id:e,name:J(e),slug:e.toLowerCase().replace(/\s+/g,"-"),count:t,isSpecial:r.includes(e.toLowerCase())})));n.sort(((e,t)=>t.count-e.count));const l=[],c=[];n.forEach((e=>{e.isSpecial?l.push(e):c.push(e)})),l.sort(((e,t)=>r.indexOf(e.id.toLowerCase())-r.indexOf(t.id.toLowerCase()))),r.forEach((e=>{l.some((t=>t.id.toLowerCase()===e))||l.push({id:e,name:J(e),slug:e,count:0,isSpecial:!0})}));const d=[...l,...c].slice(0,t);e({topCategories:d,isLoading:!1})}catch(r){e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},getCategoryBySlug:e=>{const r=t().categories.find((t=>t.slug===e));if(r)return r;if(["hot","trending","new"].includes(e.toLowerCase()))return{id:e,name:J(e),slug:e.toLowerCase()};return t().topCategories.find((t=>t.slug===e))}})));function J(e){return e.split(/[_\s]/).map((e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase())).join(" ")}const G=({icon:e,label:t,isActive:s=!1,onClick:a})=>r.jsxs("button",{className:`\n        w-full flex items-center px-4 py-2.5 text-sm rounded-lg\n        ${s?"bg-blue-700/20 text-blue-700":"text-gray-300 hover:bg-gray-800 hover:text-white"}\n        transition-colors duration-200\n      `,onClick:a,children:[r.jsx("span",{className:"flex-shrink-0 mr-3",children:e}),r.jsx("span",{children:t})]}),Q=e=>{const t=e.toLowerCase();return"hot"===t?r.jsx(O,{size:16,className:"mr-3 text-blue-700"}):"trending"===t?r.jsx(R,{size:16,className:"mr-3 text-blue-500"}):"new"===t?r.jsx(F,{size:16,className:"mr-3 text-yellow-500"}):r.jsx(q,{size:16,className:"mr-3 text-gray-400"})},K=({isOpen:t,onClose:s})=>{const a=w(),{user:o}=l(),{topCategories:n,fetchTopCategories:c,isLoading:d,error:u}=Y(),[m,h]=e.useState(!1);return e.useEffect((()=>{(async()=>{if(o)try{const{data:e,error:t}=await i.from("user_roles").select("role").eq("user_id",o.id).single();h(!t&&"admin"===e?.role)}catch(e){h(!1)}else h(!1)})()}),[o]),e.useEffect((()=>{c(6)}),[c]),r.jsxs(r.Fragment,{children:[t&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden",onClick:s}),r.jsxs("aside",{className:`\n          fixed top-0 left-0 h-full w-64 bg-gray-900 border-r border-gray-800 z-40\n          transform transition-transform duration-300 ease-in-out\n          ${t?"translate-x-0":"-translate-x-full lg:translate-x-0"}\n        `,children:[r.jsxs("div",{className:"px-4 h-16 flex items-center justify-between",children:[r.jsxs("div",{className:"font-bold text-xl text-white",children:[r.jsx("span",{className:"text-blue-500",children:"Blue"}),"Film"]}),r.jsxs("button",{className:"p-1 rounded-full text-gray-400 hover:text-white hover:bg-gray-800 lg:hidden",onClick:s,children:[r.jsx("span",{className:"sr-only",children:"Close sidebar"}),r.jsx(L,{size:20})]})]}),r.jsxs("nav",{className:"mt-2 px-2",children:[r.jsxs("div",{className:"space-y-1",children:[r.jsx(G,{icon:r.jsx(P,{size:18}),label:"Home",isActive:!0,onClick:()=>a("/",{replace:!0})}),o&&r.jsxs(r.Fragment,{children:[r.jsx(G,{icon:r.jsx(V,{size:18}),label:"Upload",onClick:()=>a("/upload")}),r.jsx(G,{icon:r.jsx(D,{size:18}),label:"My Videos",onClick:()=>a("/manage")}),m&&r.jsx(G,{icon:r.jsx(T,{size:18}),label:"Admin",onClick:()=>a("/admin")})]}),r.jsx(G,{icon:r.jsx(M,{size:18}),label:"Favorites",onClick:()=>a("/favorites")})]}),r.jsxs("div",{className:"mt-6",children:[r.jsx("h3",{className:"px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider",children:"Top Categories"}),r.jsxs("div",{className:"mt-2 space-y-1",children:[r.jsx("div",{className:"flex items-center justify-between px-4 py-2 text-sm text-blue-700 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer font-medium",onClick:()=>a("/category/hot"),children:r.jsxs("div",{className:"flex items-center",children:[r.jsx(O,{size:16,className:"mr-3 text-blue-700"}),r.jsx("span",{children:"Hot"})]})}),r.jsx("div",{className:"flex items-center justify-between px-4 py-2 text-sm text-blue-400 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer font-medium",onClick:()=>a("/category/trending"),children:r.jsxs("div",{className:"flex items-center",children:[r.jsx(R,{size:16,className:"mr-3 text-blue-500"}),r.jsx("span",{children:"Trending"})]})}),r.jsx("div",{className:"flex items-center justify-between px-4 py-2 text-sm text-yellow-400 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer font-medium",onClick:()=>a("/category/new"),children:r.jsxs("div",{className:"flex items-center",children:[r.jsx(F,{size:16,className:"mr-3 text-yellow-500"}),r.jsx("span",{children:"New"})]})})]})]}),!d&&!u&&n.filter((e=>!["hot","trending","new"].includes(e.id.toLowerCase()))).length>0&&r.jsxs("div",{className:"mt-4",children:[r.jsx("h3",{className:"px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider",children:"Categories"}),r.jsx("div",{className:"mt-2 space-y-1",children:d?r.jsx("div",{className:"px-4 py-2 text-sm text-gray-500",children:"Loading categories..."}):u?r.jsx("div",{className:"px-4 py-2 text-sm text-red-400",children:"Error loading categories"}):n.filter((e=>!["hot","trending","new"].includes(e.id.toLowerCase()))).map((e=>r.jsxs("div",{className:"flex items-center justify-between px-4 py-2 text-sm text-gray-300 hover:bg-gray-800 hover:text-white rounded-lg transition-colors duration-200 cursor-pointer",onClick:()=>a(`/category/${e.slug}`),children:[r.jsxs("div",{className:"flex items-center",children:[Q(e.name),r.jsx("span",{children:e.name})]}),r.jsx("span",{className:"text-gray-500 text-xs",children:e.count||0})]},e.id)))})]})]}),r.jsx("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:r.jsx("div",{className:"border-t border-gray-800 pt-4",children:r.jsx("div",{className:"text-xs text-gray-500",children:"© 2025 BlueFilm. All rights reserved."})})})]})]})},X=({activeTab:e="home",onTabChange:t})=>{const s=w(),{user:a}=l(),i=e=>{t&&t(e),"home"===e?s("/",{replace:!0}):"search"===e?s("/search"):"upload"===e?s("/upload"):"profile"===e&&a?s("/manage"):"profile"===e&&s("/search")};return r.jsx("div",{className:"fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-800 lg:hidden z-40",children:r.jsxs("nav",{className:"flex justify-around",children:[r.jsxs("button",{onClick:()=>i("home"),className:"flex flex-col items-center justify-center py-2 flex-1 transition-colors\n            "+("home"===e?"text-blue-700":"text-gray-400 hover:text-white"),children:[r.jsx(P,{size:20}),r.jsx("span",{className:"text-xs mt-1",children:"Home"})]}),r.jsxs("button",{onClick:()=>i("search"),className:"flex flex-col items-center justify-center py-2 flex-1 transition-colors\n            "+("search"===e?"text-blue-700":"text-gray-400 hover:text-white"),children:[r.jsx(k,{size:20}),r.jsx("span",{className:"text-xs mt-1",children:"Search"})]}),a&&r.jsxs("button",{onClick:()=>i("upload"),className:"flex flex-col items-center justify-center py-2 flex-1 transition-colors\n              "+("upload"===e?"text-blue-700":"text-gray-400 hover:text-white"),children:[r.jsx(V,{size:20}),r.jsx("span",{className:"text-xs mt-1",children:"Upload"})]}),r.jsx("button",{onClick:()=>i("profile"),className:"flex flex-col items-center justify-center py-2 flex-1 transition-colors\n            "+("profile"===e?"text-blue-700":"text-gray-400 hover:text-white"),children:a?r.jsxs(r.Fragment,{children:[r.jsx(D,{size:20}),r.jsx("span",{className:"text-xs mt-1",children:"My Videos"})]}):r.jsxs(r.Fragment,{children:[r.jsx(H,{size:20}),r.jsx("span",{className:"text-xs mt-1",children:"Profile"})]})})]})})},Z=({isOpen:t,onClose:s,initialMode:a="login"})=>{const[i,o]=e.useState(a),[n,c]=e.useState(""),[d,u]=e.useState(""),[m,h]=e.useState(null),[g,x]=e.useState(!1),{signIn:p,signUp:f}=l();e.useEffect((()=>{const e=e=>{"Escape"===e.key&&t&&s()};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)}),[t,s]);return t?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:s,children:r.jsxs("div",{className:"bg-gray-900 rounded-lg max-w-md w-full p-6",onClick:e=>e.stopPropagation(),children:[r.jsxs("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h2",{className:"text-2xl font-bold",children:"login"===i?"Sign In":"Create Account"}),r.jsx("button",{onClick:s,className:"text-gray-400 hover:text-white p-1 rounded-full hover:bg-gray-800",children:r.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[r.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),r.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]}),r.jsxs("form",{onSubmit:async e=>{e.preventDefault(),h(null),x(!0);try{"login"===i?await p(n,d):await f(n,d),s()}catch(t){h(t instanceof Error?t.message:"An error occurred")}finally{x(!1)}},className:"space-y-4",children:[r.jsx(N,{label:"Email",type:"email",value:n,onChange:e=>c(e.target.value),required:!0,fullWidth:!0}),r.jsx(N,{label:"Password",type:"password",value:d,onChange:e=>u(e.target.value),required:!0,fullWidth:!0}),m&&r.jsx("p",{className:"text-red-500 text-sm",children:m}),r.jsxs("div",{className:"space-y-3",children:[r.jsx(j,{type:"submit",variant:"primary",fullWidth:!0,isLoading:g,children:"login"===i?"Sign In":"Create Account"}),r.jsx(j,{type:"button",variant:"ghost",fullWidth:!0,onClick:s,disabled:g,children:"Cancel"})]}),r.jsx("p",{className:"text-center text-gray-400 text-sm",children:"login"===i?r.jsxs(r.Fragment,{children:["Don't have an account?"," ",r.jsx("button",{type:"button",className:"text-orange-500 hover:text-orange-400",onClick:()=>o("signup"),children:"Sign Up"})]}):r.jsxs(r.Fragment,{children:["Already have an account?"," ",r.jsx("button",{type:"button",className:"text-orange-500 hover:text-orange-400",onClick:()=>o("login"),children:"Sign In"})]})})]})]})}):null},ee=({children:t,requireAuth:s=!0,requireApproval:a=!1,redirectTo:i="/"})=>{const o=w(),{user:n,isApproved:c,isLoading:d}=l();return e.useEffect((()=>{d||(!s||n?a&&n&&!c&&o(i):o(i))}),[n,c,d,s,a,i,o]),d?r.jsx("div",{className:"min-h-screen bg-gray-900 flex items-center justify-center",children:r.jsx("div",{className:"text-white",children:"Loading..."})}):s&&!n||a&&n&&!c?null:r.jsx(r.Fragment,{children:t})},te=({isOpen:t,onAccept:s})=>{w();const[a,i]=e.useState(t);e.useEffect((()=>(i(t),document.body.style.overflow=t?"hidden":"auto",()=>{document.body.style.overflow="auto"})),[t]);return a?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4",children:r.jsx("div",{className:"bg-gray-900 rounded-lg max-w-md w-full border border-gray-700 shadow-2xl",children:r.jsxs("div",{className:"p-6",children:[r.jsx("h2",{className:"text-2xl font-bold text-white mb-4 text-center",children:"Adult Content Warning"}),r.jsx("div",{className:"h-px bg-gray-700 mb-4"}),r.jsx("p",{className:"text-gray-200 mb-6 text-center",children:"This site contains explicit adult material. You may only proceed if you are 18 years of age or older (or the legal age of majority in your jurisdiction). If you do not meet these requirements, you are not permitted to access this site."}),r.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[r.jsx("button",{onClick:()=>{i(!1),s()},className:"px-6 py-2 bg-blue-700 hover:bg-blue-600 text-white rounded-md font-medium transition-colors",children:"Enter"}),r.jsx("button",{onClick:()=>{window.location.href="https://www.google.com"},className:"px-6 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md font-medium transition-colors",children:"Leave"})]})]})})}):null},re=_((e=>({hasAcceptedDisclaimer:!1,acceptDisclaimer:()=>{const t={accepted:!0,timestamp:(new Date).getTime()};localStorage.setItem("disclaimerAccepted",JSON.stringify(t)),e({hasAcceptedDisclaimer:!0})},checkDisclaimerStatus:()=>{try{const t=localStorage.getItem("disclaimerAccepted");if(!t)return void e({hasAcceptedDisclaimer:!1});const r=JSON.parse(t);if("boolean"==typeof r||"true"===r)return void e({hasAcceptedDisclaimer:!0});r&&r.accepted?e({hasAcceptedDisclaimer:!0}):e({hasAcceptedDisclaimer:!1})}catch(t){e({hasAcceptedDisclaimer:!1})}}}))),se=e.memo((({visible:t=!1,position:s="bottom-right"})=>{const[a,i]=e.useState({fcp:null,lcp:null,cls:null,fid:null,ttfb:null,jsHeapSize:null,domNodes:null,resourceCount:null,resourceSize:null}),[o,n]=e.useState(!1),l=e=>{if(null===e)return"N/A";if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},c=e=>null===e?"N/A":`${e.toFixed(2)} ms`,d=e.useCallback((()=>{if(!window.performance||!window.performance.getEntriesByType)return;const e=performance.getEntriesByType("paint").find((e=>"first-contentful-paint"===e.name));e&&i((t=>({...t,fcp:e.startTime})))}),[]),u=e.useCallback((()=>{if(window.PerformanceObserver)try{const e=new PerformanceObserver((e=>{const t=e.getEntries(),r=t[t.length-1];r&&i((e=>({...e,lcp:r.startTime})))}));return e.observe({type:"largest-contentful-paint",buffered:!0}),()=>{e.disconnect()}}catch(e){}}),[]),m=e.useCallback((()=>{if(window.PerformanceObserver)try{let e=0;const t=new PerformanceObserver((t=>{for(const r of t.getEntries())r.hadRecentInput||(e+=r.value,i((t=>({...t,cls:e}))))}));return t.observe({type:"layout-shift",buffered:!0}),()=>{t.disconnect()}}catch(e){}}),[]),h=e.useCallback((()=>{if(window.PerformanceObserver)try{const e=new PerformanceObserver((e=>{const t=e.getEntries()[0];if(t){const e=t.processingStart-t.startTime;i((t=>({...t,fid:e})))}}));return e.observe({type:"first-input",buffered:!0}),()=>{e.disconnect()}}catch(e){}}),[]),g=e.useCallback((()=>{if(!window.performance||!window.performance.getEntriesByType)return;const e=performance.getEntriesByType("navigation")[0];e&&i((t=>({...t,ttfb:e.responseStart})))}),[]),x=e.useCallback((()=>{performance.memory&&i((e=>({...e,jsHeapSize:performance.memory.usedJSHeapSize})))}),[]),p=e.useCallback((()=>{const e=document.querySelectorAll("*").length;i((t=>({...t,domNodes:e})))}),[]),f=e.useCallback((()=>{if(!window.performance||!window.performance.getEntriesByType)return;const e=performance.getEntriesByType("resource");let t=0;e.forEach((e=>{e.transferSize&&(t+=e.transferSize)})),i((r=>({...r,resourceCount:e.length,resourceSize:t})))}),[]);return e.useEffect((()=>{if(!t)return;d(),g(),p();const e=u(),r=m(),s=h(),a=setInterval((()=>{x(),p(),f()}),2e3);return()=>{e&&e(),r&&r(),s&&s(),clearInterval(a)}}),[t,d,u,m,h,g,x,p,f]),t?r.jsxs("div",{className:`fixed ${(()=>{switch(s){case"top-right":return"top-4 right-4";case"top-left":return"top-4 left-4";case"bottom-left":return"bottom-4 left-4";default:return"bottom-4 right-4"}})()} z-50 bg-gray-900 border border-blue-700 rounded-lg shadow-lg overflow-hidden transition-all duration-300 ${o?"w-80":"w-10"}`,children:[r.jsx("button",{className:"absolute top-2 right-2 text-blue-500 hover:text-blue-400 focus:outline-none",onClick:()=>n(!o),children:o?"×":"≡"}),o&&r.jsxs("div",{className:"p-4",children:[r.jsx("h3",{className:"text-white text-sm font-bold mb-2",children:"Performance Metrics"}),r.jsxs("div",{className:"space-y-2 text-xs",children:[r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-400",children:"FCP:"}),r.jsx("span",{className:"font-mono "+(a.fcp&&a.fcp<1e3?"text-green-400":"text-yellow-400"),children:c(a.fcp)})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-400",children:"LCP:"}),r.jsx("span",{className:"font-mono "+(a.lcp&&a.lcp<2500?"text-green-400":"text-yellow-400"),children:c(a.lcp)})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-400",children:"CLS:"}),r.jsx("span",{className:"font-mono "+(a.cls&&a.cls<.1?"text-green-400":"text-yellow-400"),children:null!==a.cls?a.cls.toFixed(3):"N/A"})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-400",children:"FID:"}),r.jsx("span",{className:"font-mono "+(a.fid&&a.fid<100?"text-green-400":"text-yellow-400"),children:c(a.fid)})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-400",children:"TTFB:"}),r.jsx("span",{className:"font-mono "+(a.ttfb&&a.ttfb<200?"text-green-400":"text-yellow-400"),children:c(a.ttfb)})]}),r.jsx("div",{className:"border-t border-gray-700 my-2"}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-400",children:"JS Heap:"}),r.jsx("span",{className:"font-mono text-blue-400",children:l(a.jsHeapSize)})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-400",children:"DOM Nodes:"}),r.jsx("span",{className:"font-mono "+(a.domNodes&&a.domNodes<1500?"text-green-400":"text-yellow-400"),children:null!==a.domNodes?a.domNodes:"N/A"})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-400",children:"Resources:"}),r.jsx("span",{className:"font-mono text-blue-400",children:null!==a.resourceCount?`${a.resourceCount} (${l(a.resourceSize)})`:"N/A"})]})]})]})]}):null})),ae=({visible:t=!1,onMetricsUpdate:s})=>{const[a,i]=e.useState({imagesLoaded:0,imagesError:0,averageLoadTime:0,totalDataTransferred:0,cacheHitRate:0,slowLoadingImages:0}),[o,n]=e.useState([]),[l,c]=e.useState(null),d=e.useCallback((()=>{if(!window.PerformanceObserver)return;const e=new PerformanceObserver((e=>{e.getEntries().forEach((e=>{const t=e;if("img"===t.initiatorType||t.name.match(/\.(jpg|jpeg|png|gif|webp|avif|svg)(\?|$)/i)){const e=t.responseEnd-t.requestStart,r=t.transferSize||0;n((t=>[...t,e].slice(-100))),i((t=>{const s=t.imagesLoaded+1,a=t.totalDataTransferred+r,i=e>3e3?t.slowLoadingImages+1:t.slowLoadingImages;return{...t,imagesLoaded:s,totalDataTransferred:a,slowLoadingImages:i}}))}}))}));return e.observe({entryTypes:["resource"]}),c(e),()=>{e.disconnect()}}),[]);if(e.useEffect((()=>{if(o.length>0){const e=o.reduce(((e,t)=>e+t),0)/o.length;i((t=>({...t,averageLoadTime:e})))}}),[o]),e.useEffect((()=>{const e=setInterval((()=>{const e=u(),t=e.size>0?e.size/(a.imagesLoaded||1)*100:0;i((e=>({...e,cacheHitRate:Math.min(t,100)})))}),5e3);return()=>clearInterval(e)}),[a.imagesLoaded]),e.useEffect((()=>{if(!t)return;const e=d(),r=()=>{i((e=>({...e,imagesError:e.imagesError+1})))};return document.addEventListener("error",(e=>{e.target instanceof HTMLImageElement&&r()}),!0),()=>{e&&e(),l&&l.disconnect(),document.removeEventListener("error",r,!0)}}),[t,d,l]),e.useEffect((()=>{s&&s(a)}),[a,s]),!t)return null;const m=(e,t)=>e<=t.good?"text-green-400":e<=t.poor?"text-yellow-400":"text-red-400";return r.jsxs("div",{className:"fixed bottom-4 right-4 bg-black/90 text-white p-4 rounded-lg shadow-lg text-xs font-mono z-50 max-w-sm",children:[r.jsxs("div",{className:"flex items-center justify-between mb-2",children:[r.jsx("h3",{className:"font-bold text-sm",children:"Media Performance"}),r.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"})]}),r.jsxs("div",{className:"space-y-1",children:[r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Images Loaded:"}),r.jsx("span",{className:"text-blue-400",children:a.imagesLoaded})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Load Errors:"}),r.jsx("span",{className:a.imagesError>0?"text-red-400":"text-green-400",children:a.imagesError})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Avg Load Time:"}),r.jsx("span",{className:m(a.averageLoadTime,{good:1e3,poor:3e3}),children:(h=a.averageLoadTime,h<1e3?`${Math.round(h)}ms`:`${(h/1e3).toFixed(2)}s`)})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Data Transfer:"}),r.jsx("span",{className:"text-purple-400",children:(e=>{if(0===e)return"0 B";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]})(a.totalDataTransferred)})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Cache Hit Rate:"}),r.jsxs("span",{className:m(100-a.cacheHitRate,{good:20,poor:50}),children:[a.cacheHitRate.toFixed(1),"%"]})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Slow Images:"}),r.jsx("span",{className:a.slowLoadingImages>0?"text-red-400":"text-green-400",children:a.slowLoadingImages})]})]}),r.jsx("div",{className:"mt-3 pt-2 border-t border-gray-600",children:r.jsxs("div",{className:"flex items-center space-x-2 text-xs",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx("div",{className:"w-2 h-2 rounded-full "+(a.averageLoadTime<1e3?"bg-green-400":a.averageLoadTime<3e3?"bg-yellow-400":"bg-red-400")}),r.jsx("span",{children:"Speed"})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx("div",{className:"w-2 h-2 rounded-full "+(0===a.imagesError?"bg-green-400":"bg-red-400")}),r.jsx("span",{children:"Reliability"})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx("div",{className:"w-2 h-2 rounded-full "+(a.cacheHitRate>80?"bg-green-400":a.cacheHitRate>50?"bg-yellow-400":"bg-red-400")}),r.jsx("span",{children:"Cache"})]})]})}),navigator.connection&&r.jsx("div",{className:"mt-2 pt-2 border-t border-gray-600 text-xs",children:r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Connection:"}),r.jsx("span",{className:"text-cyan-400",children:navigator.connection.effectiveType?.toUpperCase()||"Unknown"})]})})]});var h},ie=({visible:e=!1})=>{if(!e)return null;return r.jsxs("div",{className:"fixed top-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-md z-50",children:[r.jsx("h3",{className:"font-bold mb-2",children:"Environment Variables"}),r.jsx("div",{className:"space-y-1",children:Object.entries({VITE_SUPABASE_URL:"https://vsnsglgyapexhwyfylic.supabase.co",VITE_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZzbnNnbGd5YXBleGh3eWZ5bGljIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyNjExOTEsImV4cCI6MjA2MDgzNzE5MX0.6CQWpMT14h2kaIOk1_LMECuJrfRdmiGRo3vGyEDW9tM",MODE:"production",DEV:!1,PROD:!0}).map((([e,t])=>r.jsxs("div",{className:"flex justify-between",children:[r.jsxs("span",{className:"text-gray-300",children:[e,":"]}),r.jsx("span",{className:t?"text-green-400":"text-red-400",children:e.includes("KEY")?t?"✓ Set":"✗ Missing":String(t)})]},e)))}),r.jsx("div",{className:"mt-2 pt-2 border-t border-gray-600",children:r.jsxs("div",{className:"text-gray-300",children:["URL: ",window.location.href]})})]})},oe=_()(C(((e,t)=>({watchHistory:{},likedVideos:[],watchLater:[],viewedCategories:{},isLoading:!1,error:null,updateWatchProgress:(t,r,s)=>{const a=Math.min(Math.round(r/s*100),100),o=a>=90;e((e=>({watchHistory:{...e.watchHistory,[t]:{currentTime:r,duration:s,percent:a,lastWatched:(new Date).toISOString(),completed:o}}})));(async()=>{const{data:{user:e}}=await i.auth.getUser();if(e){const{error:n}=await i.from("watch_history").upsert({user_id:e.id,video_id:t,playback_position:r,duration:s,percent:a,last_watched:(new Date).toISOString(),completed:o},{onConflict:"user_id,video_id"})}})()},getWatchProgress:e=>t().watchHistory[e]||null,getContinueWatchingVideos:async()=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await i.auth.getUser();if(!r){const r=t().watchHistory,s=new Date;s.setDate(s.getDate()-30);const a=Object.entries(r).filter((([e,t])=>{const r=new Date(t.lastWatched);return!t.completed&&r>s})).sort((([e,t],[r,s])=>new Date(s.lastWatched).getTime()-new Date(t.lastWatched).getTime())).map((([e,t])=>e));return e({isLoading:!1}),a}const{data:s,error:a}=await i.from("watch_history").select("video_id, last_watched, completed, percent").eq("user_id",r.id).eq("completed",!1).gt("percent",5).lt("percent",90).order("last_watched",{ascending:!1}).limit(10);if(a)throw new Error(a.message);const o={...t().watchHistory};return s.forEach((e=>{o[e.video_id]={currentTime:0,duration:0,percent:e.percent,lastWatched:e.last_watched,completed:e.completed}})),e({watchHistory:o,isLoading:!1}),s.map((e=>e.video_id))}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),[]}},likeVideo:async t=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await i.auth.getUser();if(!r)return e((e=>({likedVideos:[...e.likedVideos,t],isLoading:!1}))),!0;const{error:s}=await i.from("video_likes").insert({user_id:r.id,video_id:t,liked_at:(new Date).toISOString()});if(s)throw new Error(s.message);return await i.rpc("increment_video_likes",{video_id:t}),e((e=>({likedVideos:[...e.likedVideos,t],isLoading:!1}))),!0}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),!1}},unlikeVideo:async t=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await i.auth.getUser();if(!r)return e((e=>({likedVideos:e.likedVideos.filter((e=>e!==t)),isLoading:!1}))),!0;const{error:s}=await i.from("video_likes").delete().eq("user_id",r.id).eq("video_id",t);if(s)throw new Error(s.message);return await i.rpc("decrement_video_likes",{video_id:t}),e((e=>({likedVideos:e.likedVideos.filter((e=>e!==t)),isLoading:!1}))),!0}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),!1}},isVideoLiked:e=>t().likedVideos.includes(e),addToWatchLater:t=>{e((e=>({watchLater:[...e.watchLater,t]})));(async()=>{const{data:{user:e}}=await i.auth.getUser();if(e){const{error:r}=await i.from("watch_later").insert({user_id:e.id,video_id:t,added_at:(new Date).toISOString()})}})()},removeFromWatchLater:t=>{e((e=>({watchLater:e.watchLater.filter((e=>e!==t))})));(async()=>{const{data:{user:e}}=await i.auth.getUser();if(e){const{error:r}=await i.from("watch_later").delete().eq("user_id",e.id).eq("video_id",t)}})()},isInWatchLater:e=>t().watchLater.includes(e),trackCategoryView:t=>{t&&e((e=>{const r={...e.viewedCategories};return r[t]=(r[t]||0)+1,{viewedCategories:r}}))},getMostViewedCategories:(e=5)=>{const r=t().viewedCategories;return Object.entries(r).sort((([e,t],[r,s])=>s-t)).slice(0,e).map((([e,t])=>e))}})),{name:"user-preferences-storage",partialize:e=>({watchHistory:e.watchHistory,likedVideos:e.likedVideos,watchLater:e.watchLater,viewedCategories:e.viewedCategories})})),ne=_(((e,t)=>({videos:[],userVideos:[],selectedVideoIds:[],featuredVideo:null,trendingVideos:[],recommendedVideos:[],continueWatchingVideos:[],sortOptions:{field:"createdAt",direction:"desc"},filterOptions:{},pagination:{currentPage:1,totalPages:1,totalCount:0,pageSize:12},isLoading:!1,error:null,fetchVideos:async(r,s,l)=>{e({isLoading:!0,error:null});try{const c=s||t().pagination.currentPage,d=l||t().pagination.pageSize,u=(c-1)*d,m=u+d-1;if("hot"===r){const{data:t,error:r}=await a(i.from("videos")).order("views",{ascending:!1}).range(u,m);if(r)throw new Error(r.message);const{count:s}=await i.from("videos").select("id",{count:"exact"}),l=s??0,h=Math.max(1,Math.ceil(l/d)),g=t.map((e=>({id:e.id,title:e.title,description:e.description||"",thumbnailUrl:o(e.thumbnail_url||""),videoUrl:n(e.video_url||""),duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at||e.created_at,publishedAt:e.published_at||e.created_at,scheduledFor:e.scheduled_for||void 0,status:e.status||"public",isHD:e.is_hd||!1,isPremium:e.is_premium||!1,tags:Array.isArray(e.tags)?e.tags:[],category:"hot",originalCategory:e.category||"uncategorized",creator:{id:e.creator?.id||"",email:"",avatar:e.creator?.avatar_url||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}})));return void e({videos:g,pagination:{currentPage:c,totalPages:h,totalCount:l,pageSize:d},isLoading:!1})}if("new"===r){const{data:t,error:r}=await a(i.from("videos")).order("created_at",{ascending:!1}).range(u,m);if(r)throw new Error(r.message);const{count:s}=await i.from("videos").select("id",{count:"exact"}),l=s??0,h=Math.max(1,Math.ceil(l/d)),g=t.map((e=>({id:e.id,title:e.title,description:e.description||"",thumbnailUrl:o(e.thumbnail_url||""),videoUrl:n(e.video_url||""),duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at||e.created_at,publishedAt:e.published_at||e.created_at,scheduledFor:e.scheduled_for||void 0,status:e.status||"public",isHD:e.is_hd||!1,isPremium:e.is_premium||!1,tags:Array.isArray(e.tags)?e.tags:[],category:"new",originalCategory:e.category||"uncategorized",creator:{id:e.creator?.id||"",email:"",avatar:e.creator?.avatar_url||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}})));return void e({videos:g,pagination:{currentPage:c,totalPages:h,totalCount:l,pageSize:d},isLoading:!1})}if("trending"===r){const{data:t,error:r}=await a(i.from("videos")).order("views",{ascending:!1}).range(u,m);if(r)throw new Error(r.message);const{count:s}=await i.from("videos").select("id",{count:"exact"}),l=s??0,h=Math.max(1,Math.ceil(l/d)),g=t.map((e=>({id:e.id,title:e.title,description:e.description||"",thumbnailUrl:o(e.thumbnail_url||""),videoUrl:n(e.video_url||""),duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at||e.created_at,publishedAt:e.published_at||e.created_at,scheduledFor:e.scheduled_for||void 0,status:e.status||"public",isHD:e.is_hd||!1,isPremium:e.is_premium||!1,tags:Array.isArray(e.tags)?e.tags:[],category:"trending",originalCategory:e.category||"uncategorized",creator:{id:e.creator?.id||"",email:"",avatar:e.creator?.avatar_url||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}})));return void e({videos:g,pagination:{currentPage:c,totalPages:h,totalCount:l,pageSize:d},isLoading:!1})}let h=i.from("videos").select("id",{count:"exact"});r&&"all"!==r&&(h=h.eq("category",r));const{count:g,error:x}=await h;if(x)throw new Error(x.message);let p=i.from("videos");p=a(p).order("created_at",{ascending:!1}).range(u,m),r&&"all"!==r&&(p=p.eq("category",r));const{data:f,error:w}=await p;if(w)throw new Error(w.message);const v=g??0,b=Math.max(1,Math.ceil(v/d)),y=f.map((e=>({id:e.id,title:e.title,description:e.description||"",thumbnailUrl:o(e.thumbnail_url||""),videoUrl:n(e.video_url||""),duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at||e.created_at,publishedAt:e.published_at||e.created_at,scheduledFor:e.scheduled_for||void 0,status:e.status||"public",isHD:e.is_hd||!1,isPremium:e.is_premium||!1,tags:Array.isArray(e.tags)?e.tags:[],category:e.category||"uncategorized",creator:{id:e.creator?.id||"",email:"",avatar:e.creator?.avatar_url||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}})));e({videos:y,pagination:{currentPage:c,totalPages:b,totalCount:v,pageSize:d},isLoading:!1})}catch(c){e({error:c instanceof Error?c.message:"An unknown error occurred",isLoading:!1})}},fetchAllVideos:async(r,s)=>{e({isLoading:!0,error:null});try{const a=r||t().pagination.currentPage,l=s||t().pagination.pageSize,c=(a-1)*l,d=c+l-1,{count:u,error:m}=await i.from("videos").select("id",{count:"exact"});if(m)throw new Error(m.message);const{data:h,error:g}=await i.from("videos").select("\n          *,\n          profiles:user_id (\n            id,\n            username,\n            avatar_url\n          )\n        ").order("created_at",{ascending:!1}).range(c,d);if(g)throw new Error(g.message);const x=u??0,p=Math.max(1,Math.ceil(x/l)),f=h.map((e=>({id:e.id,title:e.title,description:e.description||"",thumbnailUrl:o(e.thumbnail_url||""),videoUrl:n(e.video_url||""),duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at||e.created_at,publishedAt:e.published_at||e.created_at,scheduledFor:e.scheduled_for||void 0,status:e.status||"public",isHD:e.is_hd||!1,isPremium:e.is_premium||!1,tags:Array.isArray(e.tags)?e.tags:[],category:e.category||"uncategorized",creator:{id:e.profiles?.id||"",email:"",avatar:e.profiles?.avatar_url||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}})));e({videos:f,pagination:{currentPage:a,totalPages:p,totalCount:x,pageSize:l},isLoading:!1})}catch(a){e({error:a instanceof Error?a.message:"An unknown error occurred",isLoading:!1})}},fetchVideoById:async t=>{e({isLoading:!0,error:null});try{const{data:r,error:s}=await a(i.from("videos")).eq("id",t).single();if(s)throw new Error(s.message);if(!r)return null;const l={id:r.id,title:r.title,description:r.description||"",thumbnailUrl:o(r.thumbnail_url||""),videoUrl:n(r.video_url||""),duration:r.duration||0,views:r.views||0,likes:r.likes||0,createdAt:r.created_at,updatedAt:r.updated_at||r.created_at,publishedAt:r.published_at||r.created_at,scheduledFor:r.scheduled_for||void 0,status:r.status||"public",isHD:r.is_hd||!1,isPremium:r.is_premium||!1,tags:Array.isArray(r.tags)?r.tags:[],category:r.category||"uncategorized",creator:{id:r.creator?.id||"",email:"",avatar:r.creator?.avatar_url||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}};return e({isLoading:!1}),l}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),null}},fetchFeaturedVideo:async()=>{e({isLoading:!0,error:null});try{const{data:t,error:r}=await a(i.from("videos")).order("views",{ascending:!1}).limit(1).single();if(r)throw new Error(r.message);const s={id:t.id,title:t.title,description:t.description||"",thumbnailUrl:t.thumbnail_url||"https://placehold.co/640x360/gray/white?text=No+Thumbnail",videoUrl:t.video_url,duration:t.duration||0,views:t.views||0,likes:t.likes||0,createdAt:t.created_at,updatedAt:t.updated_at||t.created_at,publishedAt:t.published_at||t.created_at,scheduledFor:t.scheduled_for||void 0,status:t.status||"public",isHD:t.is_hd||!1,isPremium:t.is_premium||!1,tags:Array.isArray(t.tags)?t.tags:[],category:t.category||"uncategorized",creator:{id:t.creator?.id||"",email:"",avatar:t.creator?.avatar_url||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}};e({featuredVideo:s,isLoading:!1})}catch(t){e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},fetchTrendingVideos:async()=>{e({isLoading:!0,error:null});try{const{data:t,error:r}=await a(i.from("videos")).order("views",{ascending:!1}).limit(8);if(r)throw new Error(r.message);const s=t.map((e=>({id:e.id,title:e.title,description:e.description||"",thumbnailUrl:e.thumbnail_url||"https://placehold.co/640x360/gray/white?text=No+Thumbnail",videoUrl:e.video_url,duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at||e.created_at,publishedAt:e.published_at||e.created_at,scheduledFor:e.scheduled_for||void 0,status:e.status||"public",isHD:e.is_hd||!1,isPremium:e.is_premium||!1,tags:Array.isArray(e.tags)?e.tags:[],category:"trending",originalCategory:e.category||"uncategorized",creator:{id:e.creator?.id||"",email:"",avatar:e.creator?.avatar_url||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}}))),o=t.map((e=>i.from("videos").update({category:"trending"}).eq("id",e.id)));await Promise.all(o),e({trendingVideos:s,isLoading:!1})}catch(t){e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},fetchRecommendedVideos:async()=>{e({isLoading:!0,error:null});try{const t=oe.getState(),r=t.watchHistory,s=Object.keys(r),o=t.likedVideos,n=a(i.from("videos")).order("views",{ascending:!1}).limit(10);s.length>0&&n.not("id","in",`(${s.join(",")})`);const{data:l,error:c}=await n;if(c)throw new Error(c.message);let d=a(i.from("videos"));if(s.length>0&&(d=d.not("id","in",`(${s.join(",")})`)),l&&l.length>0){const e=l.map((e=>e.id));d=d.not("id","in",`(${e.join(",")})`)}d=d.order("views",{ascending:!1}).limit(15);const{data:u,error:m}=await d;if(m)throw new Error(m.message);const h=[...l||[],...u||[]];if(h.length<8){const e=a(i.from("videos")).order("views",{ascending:!1}).limit(12);if(s.length>0&&e.not("id","in",`(${s.join(",")})`),h.length>0){const t=h.map((e=>e.id));e.not("id","in",`(${t.join(",")})`)}const{data:t,error:r}=await e;!r&&t&&h.push(...t)}let g=h.map((e=>{let t=0;if(o.length>0){h.some((t=>o.includes(t.id)&&t.user_id===e.user_id))&&(t+=.3)}t+=Math.min(e.views/5e3,.5);const r=new Date(e.created_at),s=new Date,a=Math.floor((s.getTime()-r.getTime())/864e5);a<30&&(t+=.2*(1-a/30));const i=e.creator,n=i&&"object"==typeof i&&"id"in i?i.id:"",l=i&&"object"==typeof i&&"avatar_url"in i?i.avatar_url:null;return{id:e.id,title:e.title,description:e.description||"",thumbnailUrl:e.thumbnail_url||"https://placehold.co/640x360/gray/white?text=No+Thumbnail",videoUrl:e.video_url,duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at,publishedAt:(new Date).toISOString(),scheduledFor:void 0,status:"public",isHD:e.is_hd||!1,isPremium:!1,tags:[],category:"uncategorized",creator:{id:n,email:"",avatar:l||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0},similarity:t}}));g=g.map((e=>({...e,similarity:e.similarity||0}))),g.sort(((e,t)=>e.views>1e3&&t.views<=1e3?-1:t.views>1e3&&e.views<=1e3?1:e.views>5e3&&t.views>5e3?t.views-e.views:(t.similarity||0)-(e.similarity||0))),g=g.slice(0,15),e({recommendedVideos:g,isLoading:!1})}catch(t){e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},fetchUserVideos:async(r,s,o,n)=>{e({isLoading:!0,error:null});try{const{data:{user:l}}=await i.auth.getUser();if(!l)throw new Error("You must be logged in to view your videos");const c=r||t().sortOptions,d=s||t().filterOptions,u=o||t().pagination.currentPage,m=n||t().pagination.pageSize,h=(u-1)*m,g=h+m-1;let x=i.from("videos").select("id",{count:"exact"}).eq("user_id",l.id);d.search&&(x=x.ilike("title",`%${d.search}%`));const{count:p,error:f}=await x;if(f)throw new Error(f.message);let w=a(i.from("videos")).eq("user_id",l.id).range(h,g);d.search&&(w=w.or(`title.ilike.%${d.search}%,description.ilike.%${d.search}%`)),d.dateRange&&(w=w.gte("created_at",d.dateRange.start).lte("created_at",d.dateRange.end));const v="createdAt"===c.field?"created_at":c.field;w=w.order(v,{ascending:"asc"===c.direction});const{data:b,error:y}=await w;if(y)throw new Error(y.message);const j=p??0,N=Math.max(1,Math.ceil(j/m)),_=b.map((e=>{const t=e.creator,r=t&&"object"==typeof t&&"id"in t?t.id:"",s=t&&"object"==typeof t&&"avatar_url"in t?t.avatar_url:null;return{id:e.id,title:e.title,description:e.description||"",thumbnailUrl:e.thumbnail_url||"https://placehold.co/640x360/gray/white?text=No+Thumbnail",videoUrl:e.video_url,duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at,publishedAt:(new Date).toISOString(),scheduledFor:void 0,status:"public",isHD:e.is_hd||!1,isPremium:!1,tags:[],category:"uncategorized",creator:{id:r,email:"",avatar:s||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0}}}));e({userVideos:_,pagination:{currentPage:u,totalPages:N,totalCount:j,pageSize:m},isLoading:!1})}catch(l){e({error:l instanceof Error?l.message:"An unknown error occurred",isLoading:!1})}},updateVideo:async(r,s)=>{e({isLoading:!0,error:null});try{const{data:{user:a}}=await i.auth.getUser();if(!a)throw new Error("You must be logged in to update videos");const o={};void 0!==s.title&&(o.title=s.title),void 0!==s.description&&(o.description=s.description),void 0!==s.category&&(o.category=s.category),void 0!==s.thumbnailUrl&&(o.thumbnail_url=s.thumbnailUrl);const{error:n}=await i.from("videos").update(o).eq("id",r).eq("user_id",a.id);if(n)throw new Error(n.message);return await t().fetchUserVideos(),e({isLoading:!1}),!0}catch(a){return e({error:a instanceof Error?a.message:"An unknown error occurred",isLoading:!1}),!1}},deleteVideo:async r=>{e({isLoading:!0,error:null});try{const{data:{user:a}}=await i.auth.getUser();if(!a)throw new Error("You must be logged in to delete videos");const{data:o,error:n}=await i.from("videos").select("*").eq("id",r).eq("user_id",a.id).single();if(n)throw new Error(n.message);if(!o)throw new Error("Video not found or you do not have permission to delete it");let l=null,c=null;if(o.video_url&&"string"==typeof o.video_url&&o.video_url.trim())try{l=new URL(o.video_url).pathname.split("/").slice(2).join("/")}catch(s){}if(o.thumbnail_url&&"string"==typeof o.thumbnail_url&&o.thumbnail_url.trim())try{c=new URL(o.thumbnail_url).pathname.split("/").slice(2).join("/")}catch(s){}if(l){const{error:e}=await i.storage.from("videos").remove([l])}if(c){const{error:e}=await i.storage.from("thumbnails").remove([c])}const{error:d}=await i.from("videos").delete().eq("id",r).eq("user_id",a.id);if(d)throw new Error(d.message);return await t().fetchUserVideos(),t().selectedVideoIds.includes(r)&&e((e=>({selectedVideoIds:e.selectedVideoIds.filter((e=>e!==r))}))),e({isLoading:!1}),!0}catch(s){return e({error:s instanceof Error?s.message:"An unknown error occurred",isLoading:!1}),!1}},updateVideoStatus:async(r,s,a)=>{e({isLoading:!0,error:null});try{const{data:{user:o}}=await i.auth.getUser();if(!o)throw new Error("You must be logged in to update video status");const n={status:s};"scheduled"===s&&a?n.scheduled_for=a:"public"===s&&(n.published_at=(new Date).toISOString());const{error:l}=await i.from("videos").update(n).eq("id",r).eq("user_id",o.id);if(l)throw new Error(l.message);return await t().fetchUserVideos(),e({isLoading:!1}),!0}catch(o){return e({error:o instanceof Error?o.message:"An unknown error occurred",isLoading:!1}),!1}},fetchVideoAnalytics:async t=>{e({isLoading:!0,error:null});try{const{data:{user:r}}=await i.auth.getUser();if(!r)throw new Error("You must be logged in to view video analytics");const{data:s,error:a}=await i.from("videos").select("*").eq("id",t).eq("user_id",r.id).single();if(a)throw new Error(a.message);if(!s)throw new Error("Video not found or you do not have permission to view its analytics");const o=Math.floor(1e3*Math.random()),n=o+Math.floor(2e3*Math.random()),l=Math.floor(.1*o),c=Math.floor(.1*n),d=Array.from({length:30},((e,t)=>{const r=new Date;return r.setDate(r.getDate()-(29-t)),{date:r.toISOString().split("T")[0],count:Math.floor(100*Math.random())}})),u=[{country:"United States",count:Math.floor(500*Math.random())},{country:"United Kingdom",count:Math.floor(300*Math.random())},{country:"Canada",count:Math.floor(200*Math.random())},{country:"Australia",count:Math.floor(150*Math.random())},{country:"Germany",count:Math.floor(100*Math.random())}],m={viewsLast7Days:o,viewsLast30Days:n,likesLast7Days:l,likesLast30Days:c,viewsByDay:d,viewsByCountry:u,viewsByDevice:[{device:"Mobile",count:Math.floor(600*Math.random())},{device:"Desktop",count:Math.floor(400*Math.random())},{device:"Tablet",count:Math.floor(200*Math.random())},{device:"Smart TV",count:Math.floor(100*Math.random())}]};return e({isLoading:!1}),m}catch(r){return e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1}),null}},fetchContinueWatchingVideos:async()=>{e({isLoading:!0,error:null});try{const t=await oe.getState().getContinueWatchingVideos();if(0===t.length)return void e({continueWatchingVideos:[],isLoading:!1});const{data:r,error:s}=await a(i.from("videos")).in("id",t).order("created_at",{ascending:!1});if(s)throw new Error(s.message);const o=r.map((e=>{const t=oe.getState().getWatchProgress(e.id),r=e.creator,s=r&&"object"==typeof r&&"id"in r?r.id:"",a=r&&"object"==typeof r&&"avatar_url"in r?r.avatar_url:null;return{id:e.id,title:e.title,description:e.description||"",thumbnailUrl:e.thumbnail_url||"https://placehold.co/640x360/gray/white?text=No+Thumbnail",videoUrl:e.video_url,duration:e.duration||0,views:e.views||0,likes:e.likes||0,createdAt:e.created_at,updatedAt:e.updated_at,publishedAt:(new Date).toISOString(),scheduledFor:void 0,status:"public",isHD:e.is_hd||!1,isPremium:!1,tags:[],category:"uncategorized",creator:{id:s,email:"",avatar:a||"https://placehold.co/150/gray/white?text=User",isVerified:!1,isCreator:!0,subscriberCount:0},progress:t||void 0}}));o.sort(((e,t)=>e.progress&&t.progress?new Date(t.progress.lastWatched).getTime()-new Date(e.progress.lastWatched).getTime():0)),e({continueWatchingVideos:o,isLoading:!1})}catch(t){e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},fetchPersonalizedContent:async()=>{e({isLoading:!0});try{await Promise.all([t().fetchContinueWatchingVideos(),t().fetchRecommendedVideos()]),e({isLoading:!1})}catch(r){e({error:r instanceof Error?r.message:"An unknown error occurred",isLoading:!1})}},selectVideo:(t,r)=>{e((e=>r?e.selectedVideoIds.includes(t)?e:{selectedVideoIds:[...e.selectedVideoIds,t]}:{selectedVideoIds:e.selectedVideoIds.filter((e=>e!==t))}))},selectAllVideos:r=>{if(r){const r=t().userVideos.map((e=>e.id));e({selectedVideoIds:r})}else e({selectedVideoIds:[]})},batchDeleteVideos:async()=>{const r=t().selectedVideoIds;if(0===r.length)return!1;e({isLoading:!0,error:null});try{const{data:{user:a}}=await i.auth.getUser();if(!a)throw new Error("You must be logged in to delete videos");const{data:o,error:n}=await i.from("videos").select("*").in("id",r).eq("user_id",a.id);if(n)throw new Error(n.message);if(!o||0===o.length)throw new Error("No videos found or you do not have permission to delete them");for(const e of o){let t=null,r=null;if(e.video_url&&"string"==typeof e.video_url&&e.video_url.trim())try{t=new URL(e.video_url).pathname.split("/").slice(2).join("/")}catch(s){}if(e.thumbnail_url&&"string"==typeof e.thumbnail_url&&e.thumbnail_url.trim())try{r=new URL(e.thumbnail_url).pathname.split("/").slice(2).join("/")}catch(s){}if(t){const{error:e}=await i.storage.from("videos").remove([t])}if(r){const{error:e}=await i.storage.from("thumbnails").remove([r])}}const{error:l}=await i.from("videos").delete().in("id",r).eq("user_id",a.id);if(l)throw new Error(l.message);return await t().fetchUserVideos(),e({selectedVideoIds:[]}),e({isLoading:!1}),!0}catch(s){return e({error:s instanceof Error?s.message:"An unknown error occurred",isLoading:!1}),!1}},batchUpdateVideosStatus:async r=>{const s=t().selectedVideoIds;if(0===s.length)return!1;e({isLoading:!0,error:null});try{const{data:{user:a}}=await i.auth.getUser();if(!a)throw new Error("You must be logged in to update videos");const o={status:r};"public"===r&&(o.published_at=(new Date).toISOString());const{error:n}=await i.from("videos").update(o).in("id",s).eq("user_id",a.id);if(n)throw new Error(n.message);return await t().fetchUserVideos(),e({isLoading:!1}),!0}catch(a){return e({error:a instanceof Error?a.message:"An unknown error occurred",isLoading:!1}),!1}},batchUpdateVideosCategory:async r=>{if(0===t().selectedVideoIds.length)return!1;e({isLoading:!0,error:null});try{const{data:{user:r}}=await i.auth.getUser();if(!r)throw new Error("You must be logged in to update videos");return await t().fetchUserVideos(),e({isLoading:!1}),!0}catch(s){return e({error:s instanceof Error?s.message:"An unknown error occurred",isLoading:!1}),!1}},incrementVideoViews:async t=>{try{const{data:r,error:s}=await i.from("videos").select("views").eq("id",t).single();if(s)return!1;const a=(r?.views||0)+1,{error:o}=await i.from("videos").update({views:a}).eq("id",t);return!o&&(e((e=>{const r=e.videos.map((e=>e.id===t?{...e,views:a}:e)),s=e.trendingVideos.map((e=>e.id===t?{...e,views:a}:e)),i=e.recommendedVideos.map((e=>e.id===t?{...e,views:a}:e)),o=e.continueWatchingVideos.map((e=>e.id===t?{...e,views:a}:e));let n=e.featuredVideo;return e.featuredVideo&&e.featuredVideo.id===t&&(n={...e.featuredVideo,views:a}),{videos:r,trendingVideos:s,recommendedVideos:i,continueWatchingVideos:o,featuredVideo:n}})),!0)}catch(r){return!1}},setSortOptions:r=>{e({sortOptions:r}),t().fetchUserVideos(r,t().filterOptions)},setFilterOptions:r=>{e({filterOptions:r}),t().fetchUserVideos(t().sortOptions,r)},setPage:r=>{e((e=>({pagination:{...e.pagination,currentPage:r}}))),t().fetchVideos(void 0,r,t().pagination.pageSize)},setPageSize:r=>{e((e=>({pagination:{...e.pagination,pageSize:r,currentPage:1}}))),t().fetchVideos(void 0,1,r)}}))),le=({visible:t=!1})=>{const[s,a]=e.useState(!1),[o,n]=e.useState(null),[l,c]=e.useState(!1),{videos:d,isLoading:u,error:m}=ne();if(!t)return null;return r.jsxs("div",{className:"fixed bottom-4 left-4 bg-black bg-opacity-90 text-white p-4 rounded-lg text-xs max-w-md z-50 max-h-96 overflow-y-auto",children:[r.jsxs("div",{className:"flex justify-between items-center mb-2",children:[r.jsx("h3",{className:"font-bold",children:"Video Debug Panel"}),r.jsx("button",{onClick:()=>a(!s),className:"text-blue-400 hover:text-blue-300",children:s?"Collapse":"Expand"})]}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-300",children:"Videos in store:"}),r.jsx("span",{className:d.length>0?"text-green-400":"text-red-400",children:d.length})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-300",children:"Store loading:"}),r.jsx("span",{className:u?"text-yellow-400":"text-green-400",children:u?"Yes":"No"})]}),m&&r.jsxs("div",{className:"text-red-400",children:[r.jsx("span",{className:"text-gray-300",children:"Store error:"})," ",m]}),r.jsx("button",{onClick:async()=>{c(!0),n(null);try{const{data:e,error:t}=await i.from("videos").select("\n          id,\n          title,\n          description,\n          thumbnail_url,\n          video_url,\n          duration,\n          views,\n          likes,\n          is_hd,\n          user_id,\n          created_at,\n          updated_at,\n          category,\n          tags,\n          creator:profiles(id, username, avatar_url)\n        ").limit(3);n(t?{success:!1,error:t.message,data:null}:{success:!0,error:null,data:e,count:e?.length||0})}catch(e){n({success:!1,error:e instanceof Error?e.message:"Unknown error",data:null})}finally{c(!1)}},disabled:l,className:"w-full bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs disabled:opacity-50",children:l?"Testing...":"Test Video Fetch"}),o&&r.jsxs("div",{className:"mt-2 p-2 bg-gray-800 rounded",children:[r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Fetch result:"}),r.jsx("span",{className:o.success?"text-green-400":"text-red-400",children:o.success?"Success":"Failed"})]}),o.error&&r.jsxs("div",{className:"text-red-400 mt-1",children:["Error: ",o.error]}),o.success&&r.jsxs("div",{className:"text-green-400 mt-1",children:["Found ",o.count," videos"]})]}),s&&r.jsxs("div",{className:"mt-4 space-y-2",children:[r.jsx("h4",{className:"font-semibold",children:"Sample Videos:"}),d.slice(0,3).map(((e,t)=>r.jsxs("div",{className:"p-2 bg-gray-800 rounded",children:[r.jsx("div",{className:"font-medium truncate",children:e.title}),r.jsxs("div",{className:"text-gray-400 text-xs",children:[r.jsxs("div",{children:["ID: ",e.id]}),r.jsxs("div",{children:["Thumbnail: ",e.thumbnailUrl?"✓":"✗"]}),r.jsxs("div",{children:["Video URL: ",e.videoUrl?"✓":"✗"]}),e.thumbnailUrl&&r.jsx("div",{className:"mt-1",children:r.jsx("button",{onClick:async()=>{var t;await(t=e.thumbnailUrl,new Promise((e=>{const r=new Image;r.onload=()=>e({success:!0,url:t}),r.onerror=()=>e({success:!1,url:t}),r.src=t})))},className:"text-blue-400 hover:text-blue-300",children:"Test Thumbnail"})}),e.videoUrl&&r.jsx("div",{className:"mt-1",children:r.jsx("button",{onClick:async()=>{await(t=e.videoUrl,new Promise((e=>{const r=document.createElement("video");r.onloadedmetadata=()=>e({success:!0,url:t,duration:r.duration}),r.onerror=()=>e({success:!1,url:t}),r.src=t})));var t},className:"text-blue-400 hover:text-blue-300",children:"Test Video"})})]})]},e.id)))]})]})]})},ce=({visible:t=!1,position:s="bottom-left"})=>{const[a,o]=e.useState(!1),[n,c]=e.useState(!1),{user:d,profile:u,isLoading:m,isApproved:h,loadUser:g}=l();if(!t)return null;return r.jsx("div",{className:`fixed ${{"top-left":"top-4 left-4","top-right":"top-4 right-4","bottom-left":"bottom-4 left-4","bottom-right":"bottom-4 right-4"}[s]} z-50`,children:r.jsxs("div",{className:"bg-gray-800 border border-gray-600 rounded-lg shadow-lg max-w-sm",children:[r.jsxs("div",{className:"flex items-center justify-between p-3 cursor-pointer bg-gray-700 rounded-t-lg",onClick:()=>o(!a),children:[r.jsx("span",{className:"text-white text-sm font-medium",children:"Auth Debug"}),r.jsx("span",{className:"text-white text-xs",children:a?"▼":"▶"})]}),a&&r.jsxs("div",{className:"p-3 space-y-3",children:[r.jsxs("div",{className:"space-y-2",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-2 h-2 rounded-full "+(m?"bg-yellow-500":"bg-gray-500")}),r.jsxs("span",{className:"text-white text-xs",children:["Loading: ",m?"Yes":"No"]})]}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-2 h-2 rounded-full "+(d?"bg-green-500":"bg-red-500")}),r.jsxs("span",{className:"text-white text-xs",children:["User: ",d?"Signed In":"Not Signed In"]})]}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-2 h-2 rounded-full "+(u?"bg-green-500":"bg-red-500")}),r.jsxs("span",{className:"text-white text-xs",children:["Profile: ",u?"Exists":"Missing"]})]}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-2 h-2 rounded-full "+(h?"bg-green-500":"bg-orange-500")}),r.jsxs("span",{className:"text-white text-xs",children:["Approved: ",h?"Yes":"No"]})]})]}),d&&r.jsx("div",{className:"border-t border-gray-600 pt-2",children:r.jsxs("div",{className:"text-white text-xs space-y-1",children:[r.jsxs("div",{children:[r.jsx("strong",{children:"ID:"})," ",d.id]}),r.jsxs("div",{children:[r.jsx("strong",{children:"Email:"})," ",d.email]}),u&&r.jsxs(r.Fragment,{children:[r.jsxs("div",{children:[r.jsx("strong",{children:"Username:"})," ",u.username]}),r.jsxs("div",{children:[r.jsx("strong",{children:"Approved:"})," ",u.is_approved?"Yes":"No"]})]})]})}),r.jsxs("div",{className:"border-t border-gray-600 pt-2 space-y-2",children:[r.jsx("button",{onClick:async()=>{c(!0);try{await g()}catch(e){}finally{c(!1)}},disabled:n,className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white text-xs py-1 px-2 rounded",children:n?"Refreshing...":"Refresh Auth"}),r.jsx("button",{onClick:async()=>{const{data:{session:e}}=await i.auth.getSession()},className:"w-full bg-gray-600 hover:bg-gray-700 text-white text-xs py-1 px-2 rounded",children:"Check Session"}),r.jsx("button",{onClick:async()=>{if(!d?.id)return;const{data:e,error:t}=await i.from("profiles").select("*").eq("id",d.id).single()},disabled:!d?.id,className:"w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-800 text-white text-xs py-1 px-2 rounded",children:"Check Profile"})]})]})]})})},de=e.lazy((()=>f((()=>import("./HomePage-DnA1_CHD.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9])))),ue=e.lazy((()=>f((()=>import("./VideoPage-DqkAF9sY.js")),__vite__mapDeps([10,1,2,5,6,7,4,8,9,11])))),me=e.lazy((()=>f((()=>import("./CategoryPage-BwGONFou.js")),__vite__mapDeps([12,1,2,9,5,6,7,3,4,8])))),he=e.lazy((()=>f((()=>import("./AllVideosPage-Zolo6wwJ.js")),__vite__mapDeps([13,1,4,5,6,7,8,9,2])))),ge=e.lazy((()=>f((()=>import("./UploadPage-Bt8MLRuf.js")),__vite__mapDeps([14,1,5,6,7,4,8,2])))),xe=e.lazy((()=>f((()=>import("./ManageVideosPage-D0SeC-xi.js")),__vite__mapDeps([15,1,4,5,6,7,8,2])))),pe=e.lazy((()=>f((()=>import("./SearchPage-CzYoNyun.js")),__vite__mapDeps([16,1,3,4,5,6,7,8,2])))),fe=e.lazy((()=>f((()=>import("./FavoritesPage-C7zymwKt.js")),__vite__mapDeps([17,1,3,4,5,6,7,8,2])))),we=e.lazy((()=>f((()=>import("./TestVideoPage-9Xn_kgid.js")),__vite__mapDeps([18,1,2,9,5,6,7,11,4,8])))),ve=e.lazy((()=>f((()=>import("./TestVideoIndexPage-CVOUSkL9.js")),__vite__mapDeps([19,1,2])))),be=e.lazy((()=>f((()=>import("./AdminPage-DB-wGY7S.js")),__vite__mapDeps([20,1,5,6,7,4,8,2]))));function ye(){const[t,s]=e.useState(!1),[a,i]=e.useState("home"),[o,n]=e.useState(!1),[c,d]=e.useState("login"),[u,x]=e.useState(!0),[p,f]=e.useState(null),[w,j]=e.useState(!1);e.useState(!1);const[N,_]=e.useState(!1);e.useState([]);const{user:C,loadUser:k}=l(),{hasAcceptedDisclaimer:L,acceptDisclaimer:E,checkDisclaimerStatus:S}=re();e.useEffect((()=>{(async()=>{try{const e=await m();_(e);const t=h();if(t.hasConflicts){t.conflicts.some((e=>e.includes("Expired")||e.includes("Corrupted")))&&j(!0)}return g((()=>{confirm("A new version is available. Refresh to update?")&&window.location.reload()}))}catch(e){f("Failed to initialize application. Please refresh the page.")}})()}),[]),e.useEffect((()=>{S(),x(!L)}),[L,S]),e.useEffect((()=>{S()}),[S]),e.useEffect((()=>{k().catch((e=>{f(`Failed to initialize authentication: ${e.message}`)}))}),[k]),e.useEffect((()=>(t?document.body.classList.add("sidebar-open"):document.body.classList.remove("sidebar-open"),()=>{document.body.classList.remove("sidebar-open")})),[t]);return e.useEffect((()=>{const e=window.location.pathname;"/"===e?i("home"):e.includes("/search")?i("search"):e.includes("/upload")?i("upload"):(e.includes("/manage")||e.includes("/profile"))&&i("profile")}),[]),p?r.jsx("div",{className:"min-h-screen bg-gray-900 text-white flex items-center justify-center",children:r.jsxs("div",{className:"text-center p-8",children:[r.jsx("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"Application Error"}),r.jsx("p",{className:"text-gray-300 mb-4",children:p}),r.jsx("button",{onClick:()=>window.location.reload(),className:"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded",children:"Reload Page"})]})}):r.jsx(v,{children:r.jsxs("div",{className:"min-h-screen bg-gray-900 text-white "+(t?"sidebar-open":""),children:[r.jsx(te,{isOpen:u,onAccept:()=>{E(),x(!1)}}),r.jsx($,{onOpenSidebar:()=>s(!0),isAuthenticated:!!C,onLoginClick:()=>{d("login"),n(!0)},onSignUpClick:()=>{d("signup"),n(!0)}}),r.jsx(K,{isOpen:t,onClose:()=>s(!1)}),r.jsx("main",{className:"pt-16 pb-20 lg:pl-64 transition-all duration-300",children:r.jsx(e.Suspense,{fallback:r.jsx("div",{className:"flex justify-center items-center h-[70vh]",children:r.jsxs("div",{className:"animate-pulse flex flex-col items-center",children:[r.jsx("div",{className:"w-16 h-16 bg-blue-700 rounded-full mb-4"}),r.jsx("div",{className:"h-4 w-32 bg-gray-700 rounded mb-2"}),r.jsx("div",{className:"h-3 w-24 bg-gray-700 rounded"})]})}),children:r.jsxs(b,{children:[r.jsx(y,{path:"/",element:r.jsx(de,{})}),r.jsx(y,{path:"/video/:id",element:r.jsx(ue,{})}),r.jsx(y,{path:"/test-video",element:r.jsx(ve,{})}),r.jsx(y,{path:"/test-video/:id",element:r.jsx(we,{})}),r.jsx(y,{path:"/category/:slug",element:r.jsx(me,{})}),r.jsx(y,{path:"/all-videos",element:r.jsx(he,{})}),r.jsx(y,{path:"/upload",element:r.jsx(ee,{requireAuth:!0,requireApproval:!0,children:r.jsx(ge,{})})}),r.jsx(y,{path:"/manage",element:r.jsx(ee,{requireAuth:!0,children:r.jsx(xe,{})})}),r.jsx(y,{path:"/search",element:r.jsx(pe,{})}),r.jsx(y,{path:"/favorites",element:r.jsx(fe,{})}),r.jsx(y,{path:"/admin",element:r.jsx(ee,{requireAuth:!0,children:r.jsx(be,{})})})]})})}),r.jsx(X,{activeTab:a,onTabChange:e=>{if(i(e),("profile"===e||"upload"===e)&&!C)return d("login"),void n(!0)}}),r.jsx(Z,{isOpen:o,onClose:()=>n(!1),initialMode:c}),r.jsx(se,{visible:!1,position:"bottom-right"}),r.jsx(ae,{visible:!1}),r.jsx(ce,{visible:!1,position:"bottom-left"}),r.jsx(ie,{visible:!1}),r.jsx(le,{visible:!1}),!1,!1,!1,!1,!1,!1]})})}class je extends e.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:r.jsx("div",{className:"min-h-screen bg-gray-900 text-white flex items-center justify-center",children:r.jsxs("div",{className:"text-center p-8 max-w-md",children:[r.jsx("h1",{className:"text-2xl font-bold text-red-400 mb-4",children:"Something went wrong"}),r.jsx("p",{className:"text-gray-300 mb-4",children:"We encountered an unexpected error. Please try refreshing the page."}),!1,r.jsx("button",{onClick:()=>window.location.reload(),className:"mt-4 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors",children:"Reload Page"})]})}):this.props.children}}x();const Ne=document.getElementById("root");if(!Ne)throw new Error("Root element not found");s(Ne).render(r.jsx(e.StrictMode,{children:r.jsx(je,{children:r.jsx(p,{value:{errorRetryCount:3,errorRetryInterval:1e3,dedupingInterval:5e3,focusThrottleInterval:1e4,revalidateOnFocus:!1,revalidateIfStale:!0,revalidateOnReconnect:!0,onError:e=>{},onSuccess:(e,t)=>{}},children:r.jsx(ye,{})})})}));export{W as S,Y as a,B as b,oe as c,ne as u};
