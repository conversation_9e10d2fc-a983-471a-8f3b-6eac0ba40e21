import{r as e,j as s}from"./react-core-B9nwsbCA.js";import{d as r,u as t,L as a}from"./router-BDggJ1ol.js";import{a as d}from"./useVideos-BvCn9-Hj.js";import{N as i}from"./NativeVideoPlayer-C7XQiAop.js";import"./utils-CThq6s06.js";import"./state-DYB6TP8I.js";import"./supabase-C-51sAsE.js";import"./main-ChYA8dGC.js";import"./ui-components-DIfZPJu4.js";import"./icons-BWE0bDFO.js";const o=["5ac4509c-ed57-49fc-a160-2ed92f72d95e","ef3f0da2-836d-4370-a3f1-14a4f81950d7","30ac95c9-2e7e-4254-a858-f919fa319799","cbf8d650-bac5-4b50-821e-d6770493fe85","1fbb411b-211a-4344-98a1-c5f43739f643"],l=()=>{const{id:l}=r(),c=t();e.useEffect((()=>{l&&!o.includes(l)&&/^\d+$/.test(l)&&c(`/test-video/${o[0]}`,{replace:!0})}),[l,c]);const{video:n,isLoading:m,error:x}=d(l||"");return m?s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsx("div",{className:"flex justify-center items-center h-64",children:s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-700"})})}):x||!n?s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto mb-8",children:[s.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Error Loading Video"}),s.jsx("p",{className:"text-red-200 mb-4",children:x||"Video not found"}),s.jsx("p",{className:"text-white mb-4",children:"Please try one of these valid video IDs:"}),s.jsx("div",{className:"grid grid-cols-1 gap-2",children:o.map((e=>s.jsx(a,{to:`/test-video/${e}`,className:"bg-blue-700 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors",children:e},e)))})]})}):s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsx("h1",{className:"text-2xl font-bold mb-4",children:n.title}),s.jsx(i,{video:n,onBack:()=>c(-1)}),s.jsxs("div",{className:"mt-8 bg-gray-800 rounded-lg p-4",children:[s.jsx("h2",{className:"text-xl font-bold mb-4",children:"Test Other Videos"}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:o.filter((e=>e!==l)).map((e=>s.jsxs(a,{to:`/test-video/${e}`,className:"bg-blue-700 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors",children:["Try Video ID: ",e.substring(0,8),"..."]},e)))})]})]})};export{l as default};
