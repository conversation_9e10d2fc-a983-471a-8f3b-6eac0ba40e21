import{r as e,j as t}from"./react-core-B9nwsbCA.js";import{c as n,u as r}from"./main-ChYA8dGC.js";import{J as s}from"./utils-CThq6s06.js";const i=e.memo((({video:i,onBack:a})=>{const[o,c]=e.useState(!1),[d,l]=e.useState(0),[u,v]=e.useState(i.duration||0),[m,f]=e.useState(s()),[E,h]=e.useState(!1),[p,L]=e.useState(!1),b=e.useRef(null),w=e.useRef(null),g=e.useRef(!1),{updateWatchProgress:k,getWatchProgress:x}=n(),{incrementVideoViews:j}=r();return e.useEffect((()=>{const e=navigator.userAgent||navigator.vendor||window.opera;L(/iPad|iPhone|iPod/.test(e)&&!window.MSStream),h(/Android/i.test(e))}),[]),e.useEffect((()=>{const e=()=>{f(s())};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}}),[]),e.useEffect((()=>{const e=()=>{b.current&&!isInFullscreen()&&b.current.paused};return document.addEventListener("fullscreenchange",e),document.addEventListener("webkitfullscreenchange",e),document.addEventListener("mozfullscreenchange",e),document.addEventListener("MSFullscreenChange",e),()=>{document.removeEventListener("fullscreenchange",e),document.removeEventListener("webkitfullscreenchange",e),document.removeEventListener("mozfullscreenchange",e),document.removeEventListener("MSFullscreenChange",e)}}),[]),e.useEffect((()=>{if(!b.current)return;const e=()=>{if(b.current){const e=b.current.currentTime,t=b.current.duration,n=e/t*100;l(e),v(t),Math.floor(e)%5==0&&k(i.id,e,t),n>=10&&!g.current&&(j(i.id),g.current=!0)}},t=()=>c(!0),n=()=>c(!1),r=()=>{c(!1),u>0&&k(i.id,u,u)},s=()=>{if(b.current){v(b.current.duration);const e=x(i.id);e&&!e.completed&&(b.current.currentTime=e.currentTime,l(e.currentTime))}},a=()=>{E&&b.current&&(isInFullscreen()?exitFullscreen():requestFullscreen(b.current))},o=b.current;return o.addEventListener("timeupdate",e),o.addEventListener("play",t),o.addEventListener("pause",n),o.addEventListener("ended",r),o.addEventListener("loadedmetadata",s),E&&o.addEventListener("dblclick",a),p&&(o.addEventListener("webkitbeginfullscreen",(()=>{})),o.addEventListener("webkitendfullscreen",(()=>{}))),()=>{o.removeEventListener("timeupdate",e),o.removeEventListener("play",t),o.removeEventListener("pause",n),o.removeEventListener("ended",r),o.removeEventListener("loadedmetadata",s),E&&o.removeEventListener("dblclick",a),p&&(o.removeEventListener("webkitbeginfullscreen",(()=>{})),o.removeEventListener("webkitendfullscreen",(()=>{}))),u>0&&k(i.id,d,u)}}),[i.id,k,j,x,d,u,p,E]),t.jsxs("div",{className:"bg-black video-player-container relative z-10",children:[t.jsx("div",{ref:w,className:"relative aspect-video max-h-[70vh] bg-black video-container",children:t.jsx("video",{ref:b,className:"w-full h-full object-contain bg-black native-video-player",src:i.videoUrl,poster:i.thumbnailUrl,controls:!0,playsInline:!0,"webkit-playsinline":"true","x-webkit-airplay":"allow",preload:"metadata",autoPlay:!0,controlsList:"nodownload",disablePictureInPicture:m})}),a&&t.jsx("div",{className:"mt-4 flex justify-start",children:t.jsxs("button",{className:"bg-blue-700 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition-colors flex items-center",onClick:a,"aria-label":"Back to previous page",children:[t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2",children:t.jsx("path",{d:"M19 12H5M12 19l-7-7 7-7"})}),"Back"]})})]})}));export{i as N};
