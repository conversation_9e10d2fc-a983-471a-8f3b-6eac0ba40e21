import{r as e,j as s}from"./react-core-B9nwsbCA.js";import{u as r,s as a}from"./utils-CThq6s06.js";import{B as t}from"./ui-components-DIfZPJu4.js";import{u as l}from"./router-BDggJ1ol.js";import{d as i,K as d,N as c}from"./icons-BWE0bDFO.js";import"./state-DYB6TP8I.js";import"./supabase-C-51sAsE.js";const n=()=>{const n=l(),{user:o,isLoading:x}=r(),[m,p]=e.useState(!1),[u,h]=e.useState(!0),[j,f]=e.useState([]),[b,y]=e.useState(!1),[N,g]=e.useState(null),[v,w]=e.useState(null);e.useEffect((()=>{(async()=>{if(o)try{const{data:e,error:s}=await a.from("user_roles").select("role").eq("user_id",o.id).single();p(!s&&"admin"===e?.role)}catch(e){p(!1)}finally{h(!1)}else h(!1)})()}),[o]),e.useEffect((()=>{x||o||n("/")}),[o,x,n]),e.useEffect((()=>{m&&!u&&(async()=>{if(m){y(!0),g(null);try{const{data:e,error:s}=await a.from("profiles").select("*").eq("is_approved",!1).order("created_at",{ascending:!1});if(s)throw s;f(e||[])}catch(e){g("Failed to load pending users. Please try again.")}finally{y(!1)}}})()}),[m,u]);return x||u?s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsx("div",{className:"flex justify-center items-center h-64",children:s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})})}):o?m?s.jsxs("div",{className:"container mx-auto px-4 py-8",children:[s.jsx("h1",{className:"text-2xl md:text-3xl font-bold mb-6",children:"Admin Dashboard"}),s.jsxs("div",{className:"bg-gray-800 rounded-lg p-6",children:[s.jsxs("h2",{className:"text-xl font-semibold mb-4 flex items-center",children:[s.jsx(d,{className:"mr-2"})," Pending User Approvals"]}),N&&s.jsx("div",{className:"bg-red-900/30 border border-red-500 text-red-300 px-4 py-3 rounded mb-4",children:N}),v&&s.jsx("div",{className:"bg-green-900/30 border border-green-500 text-green-300 px-4 py-3 rounded mb-4",children:v}),b?s.jsx("div",{className:"flex justify-center py-8",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"})}):0===j.length?s.jsx("div",{className:"text-center py-8 text-gray-400",children:"No pending users to approve"}):s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"w-full",children:[s.jsx("thead",{children:s.jsxs("tr",{className:"border-b border-gray-700",children:[s.jsx("th",{className:"px-4 py-2 text-left",children:"Username"}),s.jsx("th",{className:"px-4 py-2 text-left",children:"Email"}),s.jsx("th",{className:"px-4 py-2 text-left",children:"Registered"}),s.jsx("th",{className:"px-4 py-2 text-right",children:"Actions"})]})}),s.jsx("tbody",{children:j.map((e=>s.jsxs("tr",{className:"border-b border-gray-700",children:[s.jsx("td",{className:"px-4 py-3",children:e.username}),s.jsx("td",{className:"px-4 py-3",children:e.email||"N/A"}),s.jsx("td",{className:"px-4 py-3",children:new Date(e.created_at).toLocaleDateString()}),s.jsx("td",{className:"px-4 py-3 text-right",children:s.jsx(t,{variant:"success",size:"sm",leftIcon:s.jsx(c,{size:16}),onClick:()=>(async e=>{try{g(null);const{error:s}=await a.from("profiles").update({is_approved:!0}).eq("id",e);if(s)throw s;f(j.filter((s=>s.id!==e))),w("User approved successfully"),setTimeout((()=>w(null)),3e3)}catch(s){g("Failed to approve user. Please try again.")}})(e.id),children:"Approve"})})]},e.id)))})]})})]})]}):s.jsx("div",{className:"container mx-auto px-4 py-8",children:s.jsx("div",{className:"bg-gray-800 rounded-lg p-6",children:s.jsxs("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[s.jsx(i,{size:48,className:"text-red-500 mb-4"}),s.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Access Denied"}),s.jsx("p",{className:"text-gray-400 max-w-md",children:"You don't have permission to access the admin area."})]})})}):null};export{n as default};
