import{j as e,r as s}from"./react-core-B9nwsbCA.js";import{u as a,b as t,c as r}from"./router-BDggJ1ol.js";import{V as o}from"./VideoGrid-BixslH7t.js";import{P as i}from"./ui-components-DIfZPJu4.js";import{S as n}from"./main-ChYA8dGC.js";import{u as d}from"./useVideos-BvCn9-Hj.js";import"./utils-CThq6s06.js";import"./state-DYB6TP8I.js";import"./supabase-C-51sAsE.js";import"./icons-BWE0bDFO.js";const c=({onCategoryChange:s,selectedCategory:a})=>e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex space-x-3 sm:space-x-6 md:space-x-8 py-2 overflow-x-auto scrollbar-hide",children:[e.jsx("button",{className:"px-3 py-2 sm:px-4 rounded-full whitespace-nowrap transition-colors text-sm sm:text-base "+(""===a?"bg-blue-700 text-white":"bg-gray-800 text-gray-300 hover:bg-gray-700"),onClick:()=>s(""),children:"All Videos"}),[{id:"trending",name:"Trending",slug:"trending"},{id:"recommended",name:"Recommended",slug:"recommended"},{id:"hot",name:"Hot",slug:"hot"},{id:"new",name:"New",slug:"new"}].map((t=>e.jsx("button",{className:"px-3 py-2 sm:px-4 rounded-full whitespace-nowrap transition-colors text-sm sm:text-base "+(a===t.slug?"bg-blue-700 text-white":"bg-gray-800 text-gray-300 hover:bg-gray-700"),onClick:()=>s(t.slug),children:t.name},t.id)))]})}),l=()=>{const l=a(),m=t(),[g,x]=r(),h=parseInt(g.get("page")||"1"),p=g.get("category")||"",[b,u]=s.useState(h),[j,N]=s.useState(p);s.useEffect((()=>{if("/"===m.pathname&&!g.has("page")){u(1);const e={page:"1"};j&&(e.category=j),x(e)}}),[m.pathname,g,x,j]),s.useEffect((()=>{const e=parseInt(g.get("page")||"1"),s=g.get("category")||"";e!==b&&u(e),s!==j&&N(s)}),[g,b,j]);const{videos:v,pagination:y,isLoading:f,error:w}=d(j,b,24);return w&&!v.length?e.jsx("div",{className:"container mx-auto px-4 py-16",children:e.jsxs("div",{className:"bg-red-500/20 border border-red-500 rounded-md p-4 max-w-2xl mx-auto",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Error Loading Videos"}),e.jsx("p",{className:"text-red-200",children:w})]})}):e.jsx("div",{className:"pb-10",children:e.jsxs("div",{className:"container mx-auto px-4",children:[e.jsx("div",{className:"md:hidden my-4",children:e.jsx("div",{className:"relative search-bar-container px-1",children:e.jsx(n,{className:"w-full mobile-search-bar homepage-search"})})}),e.jsx("div",{className:"mb-6",children:e.jsx(c,{selectedCategory:j,onCategoryChange:e=>{N(e),u(1);const s={page:"1"};e&&(s.category=e),x(s),window.scrollTo({top:0,behavior:"smooth"})}})}),w&&e.jsxs("div",{className:"bg-red-500/20 border border-red-500 text-white p-4 rounded-lg mb-6",children:[e.jsx("h3",{className:"font-bold mb-2",children:"Error Loading Videos"}),e.jsx("p",{children:w})]}),e.jsx("div",{className:"mt-6",children:e.jsx(o,{videos:v,onVideoClick:e=>{l(`/video/${e.id}`)},isLoading:f,className:"mb-8"})}),!f&&v.length>0&&y.totalPages>1&&e.jsx("div",{className:"mt-12 mb-8",children:e.jsx(i,{currentPage:y.currentPage,totalPages:y.totalPages,onPageChange:e=>{u(e);const s={page:e.toString()};j&&(s.category=j),x(s),window.scrollTo({top:0,behavior:"smooth"})},className:"justify-center"})}),!f&&0===v.length&&!w&&e.jsxs("div",{className:"text-center py-16",children:[e.jsx("div",{className:"text-gray-400 text-lg mb-4",children:"No videos found"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Check back later for new content!"})]})]})})};export{l as default};
