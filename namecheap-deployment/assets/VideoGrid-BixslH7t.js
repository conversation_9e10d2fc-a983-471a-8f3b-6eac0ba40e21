import{j as e,r as s}from"./react-core-B9nwsbCA.js";import{V as t,a as i,b as o,c as r}from"./ui-components-DIfZPJu4.js";import{w as l}from"./utils-CThq6s06.js";const n=({title:n,videos:a,onVideoClick:c,isLoading:d=!1,viewAll:m,compact:x=!1,className:p="",showProgressBar:g=!1})=>{const h=Array(x?6:8).fill(0).map(((s,i)=>e.jsx(t,{compact:x},`skeleton-${i}`))),j=s.useMemo((()=>d||0===a.length?[]:l(a)),[a,d]),u=e=>{if(!c)return;const s=a.find((s=>s.id===e));s&&c(s)};return e.jsxs("section",{className:`py-5 ${p}`,children:[n&&e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("h2",{className:"text-xl md:text-2xl font-bold text-white group",children:[n,!x&&e.jsx("div",{className:"h-1 w-0 group-hover:w-full bg-blue-700 transition-all duration-300 mt-1"})]}),e.jsxs("div",{className:"flex items-center",children:[d&&a.length>0&&e.jsx("div",{className:"w-5 h-5 border-t-2 border-b-2 border-blue-700 rounded-full animate-spin mr-3"}),m&&e.jsx("button",{onClick:m,className:"text-gray-400 hover:text-blue-700 text-sm font-medium transition-colors",children:"View All"})]})]}),x?e.jsxs("div",{className:"relative group",children:[e.jsx("div",{className:"overflow-x-auto scrollbar-hide pb-4",children:e.jsxs("div",{className:"flex space-x-4",style:{minWidth:"max-content"},children:[d&&0===a.length?h:j.map(((s,t)=>{if("baseTitle"in s&&"videos"in s){const o=s;return e.jsx(i,{series:o,onClick:u,compact:!0},`series-${t}`)}{const t=s;return e.jsx("div",{className:"w-[280px]",children:t.progress&&g?e.jsx(o,{video:t,onClick:c}):e.jsx(r,{video:t,onClick:c})},t.id)}})),!d&&0===a.length&&e.jsx("div",{className:"py-8 text-center text-gray-400 w-full",children:e.jsx("p",{children:"No videos found"})})]})}),e.jsx("div",{className:"absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-900 to-transparent pointer-events-none"}),e.jsx("div",{className:"absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-900 to-transparent pointer-events-none"})]}):e.jsxs("div",{className:"grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-4",children:[d&&0===a.length?h:j.map(((s,t)=>{if("baseTitle"in s&&"videos"in s){const o=s;return e.jsx(i,{series:o,onClick:u},`series-${t}`)}{const t=s;return t.progress&&g?e.jsx(o,{video:t,onClick:c},t.id):e.jsx(r,{video:t,onClick:c},t.id)}})),!d&&0===a.length&&e.jsx("div",{className:"col-span-full py-8 text-center text-gray-400",children:e.jsx("p",{children:"No videos found"})})]})]})};export{n as V};
