<?php
/**
 * Secure File Upload Handler for Namecheap Hosting
 * Replaces Supabase storage functionality
 */

// Enable error reporting for debugging (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// CORS headers for frontend access
header('Access-Control-Allow-Origin: *'); // Allow all origins for now
header('Access-Control-Allow-Methods: POST, OPTIONS, GET');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration
define('MAX_FILE_SIZE', 100 * 1024 * 1024); // 100MB
define('UPLOAD_DIR', '../media/');
define('VIDEOS_DIR', UPLOAD_DIR . 'videos/');
define('THUMBNAILS_DIR', UPLOAD_DIR . 'thumbnails/');
define('ALLOWED_VIDEO_TYPES', ['mp4', 'mov', 'avi', 'mkv', 'webm']);
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'webp', 'gif']);

// Ensure upload directories exist
function createDirectories() {
    $dirs = [UPLOAD_DIR, VIDEOS_DIR, THUMBNAILS_DIR];
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}

// Validate file type
function validateFileType($filename, $type) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    
    if ($type === 'video') {
        return in_array($extension, ALLOWED_VIDEO_TYPES);
    } elseif ($type === 'image') {
        return in_array($extension, ALLOWED_IMAGE_TYPES);
    }
    
    return false;
}

// Sanitize filename
function sanitizeFilename($filename) {
    // Remove any path components
    $filename = basename($filename);
    
    // Replace special characters with underscores
    $filename = preg_replace('/[^a-zA-Z0-9.-]/', '_', $filename);
    
    // Replace multiple underscores with single
    $filename = preg_replace('/_+/', '_', $filename);
    
    return strtolower($filename);
}

// Generate unique filename
function generateUniqueFilename($originalName, $userId) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $timestamp = time();
    $random = bin2hex(random_bytes(8));
    return $userId . '_' . $timestamp . '_' . $random . '.' . $extension;
}

// Validate user authentication (basic implementation)
function validateUser() {
    // Get authorization header
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    
    if (empty($authHeader)) {
        return null;
    }
    
    // Extract token (Bearer token format)
    if (strpos($authHeader, 'Bearer ') === 0) {
        $token = substr($authHeader, 7);
        
        // Here you would validate the token against your auth system
        // For now, we'll do a basic validation
        if (strlen($token) > 20) {
            // Return a mock user ID - replace with actual validation
            return hash('sha256', $token . 'salt') . substr(md5($token), 0, 8);
        }
    }
    
    return null;
}

// Handle chunked upload
function handleChunkedUpload($file, $chunkIndex, $totalChunks, $filename, $userId, $type) {
    $tempDir = sys_get_temp_dir() . '/uploads/';
    if (!is_dir($tempDir)) {
        mkdir($tempDir, 0755, true);
    }
    
    $chunkFile = $tempDir . $filename . '.part' . $chunkIndex;
    
    // Save chunk
    if (!move_uploaded_file($file['tmp_name'], $chunkFile)) {
        throw new Exception('Failed to save chunk');
    }
    
    // If this is the last chunk, combine all chunks
    if ($chunkIndex == $totalChunks - 1) {
        $targetDir = ($type === 'video') ? VIDEOS_DIR : THUMBNAILS_DIR;
        $userDir = $targetDir . $userId . '/';
        
        if (!is_dir($userDir)) {
            mkdir($userDir, 0755, true);
        }
        
        $finalFile = $userDir . $filename;
        $output = fopen($finalFile, 'wb');
        
        if (!$output) {
            throw new Exception('Failed to create final file');
        }
        
        // Combine chunks
        for ($i = 0; $i < $totalChunks; $i++) {
            $chunkPath = $tempDir . $filename . '.part' . $i;
            if (!file_exists($chunkPath)) {
                fclose($output);
                throw new Exception('Missing chunk: ' . $i);
            }
            
            $chunk = fopen($chunkPath, 'rb');
            stream_copy_to_stream($chunk, $output);
            fclose($chunk);
            unlink($chunkPath); // Clean up chunk
        }
        
        fclose($output);
        
        // Verify file size
        if (filesize($finalFile) === 0) {
            unlink($finalFile);
            throw new Exception('Final file is empty');
        }
        
        return $finalFile;
    }
    
    return null; // Not the last chunk
}

// Handle regular upload
function handleRegularUpload($file, $filename, $userId, $type) {
    $targetDir = ($type === 'video') ? VIDEOS_DIR : THUMBNAILS_DIR;
    $userDir = $targetDir . $userId . '/';
    
    if (!is_dir($userDir)) {
        mkdir($userDir, 0755, true);
    }
    
    $targetFile = $userDir . $filename;
    
    if (!move_uploaded_file($file['tmp_name'], $targetFile)) {
        throw new Exception('Failed to move uploaded file');
    }
    
    return $targetFile;
}

// Generate public URL
function generatePublicUrl($filePath) {
    $relativePath = str_replace('../', '', $filePath);
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    return $protocol . '://' . $host . '/' . $relativePath;
}

// Main upload handler
try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests allowed');
    }
    
    // Validate user
    $userId = validateUser();
    if (!$userId) {
        http_response_code(401);
        echo json_encode(['error' => 'Unauthorized']);
        exit();
    }
    
    // Create directories
    createDirectories();
    
    // Check if file was uploaded
    if (!isset($_FILES['file'])) {
        throw new Exception('No file uploaded');
    }
    
    $file = $_FILES['file'];
    $type = $_POST['type'] ?? 'video'; // 'video' or 'image'
    
    // Validate file size
    if ($file['size'] > MAX_FILE_SIZE) {
        throw new Exception('File too large. Maximum size: ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB');
    }
    
    // Validate file type
    if (!validateFileType($file['name'], $type)) {
        throw new Exception('Invalid file type');
    }
    
    // Sanitize and generate filename
    $originalName = sanitizeFilename($file['name']);
    $filename = generateUniqueFilename($originalName, $userId);
    
    // Handle chunked upload if specified
    $chunkIndex = isset($_POST['chunkIndex']) ? (int)$_POST['chunkIndex'] : null;
    $totalChunks = isset($_POST['totalChunks']) ? (int)$_POST['totalChunks'] : null;
    
    if ($chunkIndex !== null && $totalChunks !== null) {
        $finalFile = handleChunkedUpload($file, $chunkIndex, $totalChunks, $filename, $userId, $type);
        
        if ($finalFile === null) {
            // Not the last chunk
            echo json_encode([
                'success' => true,
                'message' => 'Chunk uploaded',
                'chunkIndex' => $chunkIndex,
                'totalChunks' => $totalChunks
            ]);
            exit();
        }
    } else {
        $finalFile = handleRegularUpload($file, $filename, $userId, $type);
    }
    
    // Generate public URL
    $publicUrl = generatePublicUrl($finalFile);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'url' => $publicUrl,
        'filename' => $filename,
        'size' => filesize($finalFile),
        'type' => $type
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'error' => $e->getMessage()
    ]);
}
?>
