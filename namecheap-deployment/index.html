<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#1f2937" />
    <base href="/" />
    <title>BlueFilm - Video Streaming Platform</title>
    <meta name="description" content="A modern video sharing platform built with React, TypeScript, and Supabase." />

    <!-- Preload critical assets -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://vsnsglgyapexhwyfylic.supabase.co" crossorigin />

    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="https://avatars.dicebear.com" />

    <!-- CSS will be loaded by Vite automatically -->
    <script type="module" crossorigin src="/assets/main-ChYA8dGC.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/react-core-B9nwsbCA.js">
    <link rel="modulepreload" crossorigin href="/assets/state-DYB6TP8I.js">
    <link rel="modulepreload" crossorigin href="/assets/supabase-C-51sAsE.js">
    <link rel="modulepreload" crossorigin href="/assets/utils-CThq6s06.js">
    <link rel="modulepreload" crossorigin href="/assets/router-BDggJ1ol.js">
    <link rel="modulepreload" crossorigin href="/assets/icons-BWE0bDFO.js">
    <link rel="modulepreload" crossorigin href="/assets/ui-components-DIfZPJu4.js">
    <link rel="stylesheet" crossorigin href="/assets/main-BrYBmeCN.css">
  </head>
  <body>
    <div id="root"></div>
    <!-- Clear any cached service workers that might be causing issues -->
    <script src="/sw-unregister.js"></script>
  </body>
</html>
